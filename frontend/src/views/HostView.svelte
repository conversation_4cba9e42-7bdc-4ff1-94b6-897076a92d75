<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { push } from 'svelte-spa-router';
  import { webSocketService } from '../services/websocket';
  import { apiService } from '../services/api';
  import type { Quiz, Game, Player, QuizQuestion, GameState, Packet } from '../types/quiz';
  import { PacketTypes } from '../types/quiz';
  import HostLobbyView from '../components/HostLobbyView.svelte';
  import HostGameView from '../components/HostGameView.svelte';
  import HostResultsView from '../components/HostResultsView.svelte';
  import LoadingSpinner from '../components/LoadingSpinner.svelte';

  // URL parameters
  export let params: { gameId?: string } = {};

  // Svelte 5 runes for state management
  let quizzes = $state<Quiz[]>([]);
  let selectedQuiz = $state<Quiz | null>(null);
  let game = $state<Game | null>(null);
  let players = $state<Player[]>([]);
  let gameState = $state<GameState>('lobby' as GameState);
  let currentQuestion = $state<QuizQuestion | null>(null);
  let currentQuestionIndex = $state(0);
  let isConnected = $state(false);
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let showQuizSelector = $state(true);

  onMount(async () => {
    if (params.gameId) {
      // Load existing game
      await loadExistingGame(params.gameId);
    } else {
      // Load quizzes for selection
      await loadQuizzes();
    }
  });

  onDestroy(() => {
    webSocketService.disconnect();
  });

  async function loadQuizzes() {
    isLoading = true;
    try {
      quizzes = await apiService.getQuizzes();
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load quizzes';
    } finally {
      isLoading = false;
    }
  }

  async function loadExistingGame(gameId: string) {
    isLoading = true;
    try {
      // In a real app, you'd have an API endpoint to get game details
      // For now, we'll connect to WebSocket and assume the game exists
      await connectToWebSocket();
      showQuizSelector = false;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load game';
    } finally {
      isLoading = false;
    }
  }

  async function selectQuiz(quiz: Quiz) {
    selectedQuiz = quiz;
    try {
      // Create a new game
      game = await apiService.createGame(quiz.id);
      if (game) {
        await connectToWebSocket();
        showQuizSelector = false;
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to create game';
    }
  }

  async function connectToWebSocket() {
    try {
      await webSocketService.connect();
      webSocketService.onPacket(handlePacket);
      
      if (selectedQuiz && !params.gameId) {
        // Host a new game
        webSocketService.sendHostGame(selectedQuiz.id);
      }
      
      isConnected = true;
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to connect to WebSocket';
    }
  }

  function handlePacket(packet: Packet) {
    console.log('Host received packet:', packet);

    switch (packet.id) {
      case PacketTypes.HostGame:
        const hostPacket = packet as any;
        if (hostPacket.gameId && hostPacket.code) {
          game = {
            ...game,
            id: hostPacket.gameId,
            code: hostPacket.code
          } as Game;
        }
        break;

      case PacketTypes.PlayerJoin:
        const joinPacket = packet as any;
        if (joinPacket.player) {
          players = [...players, joinPacket.player];
        }
        break;

      case PacketTypes.PlayerDisconnect:
        const disconnectPacket = packet as any;
        players = players.filter(p => p.id !== disconnectPacket.playerId);
        break;

      case PacketTypes.ChangeGameState:
        const statePacket = packet as any;
        const stateMap = {
          0: 'lobby',
          1: 'play',
          2: 'intermission',
          3: 'reveal',
          4: 'end'
        };
        gameState = stateMap[statePacket.state] as GameState || 'lobby' as GameState;
        break;
    }
  }

  function startGame() {
    if (!selectedQuiz || selectedQuiz.questions.length === 0) {
      error = 'Quiz must have at least one question';
      return;
    }

    webSocketService.sendStartGame();
    currentQuestionIndex = 0;
    currentQuestion = selectedQuiz.questions[0];
    gameState = 'play' as GameState;
  }

  function showQuestion() {
    if (!currentQuestion) return;
    
    webSocketService.sendQuestionShow(currentQuestion.id);
    webSocketService.sendChangeGameState(1); // Play state
  }

  function revealAnswers() {
    webSocketService.sendChangeGameState(3); // Reveal state
  }

  function nextQuestion() {
    if (!selectedQuiz) return;

    currentQuestionIndex++;
    if (currentQuestionIndex < selectedQuiz.questions.length) {
      currentQuestion = selectedQuiz.questions[currentQuestionIndex];
      webSocketService.sendChangeGameState(2); // Intermission state
    } else {
      // End game
      webSocketService.sendChangeGameState(4); // End state
      gameState = 'end' as GameState;
    }
  }

  function endGame() {
    webSocketService.sendChangeGameState(4); // End state
    gameState = 'end' as GameState;
  }

  function backToQuizSelection() {
    webSocketService.disconnect();
    showQuizSelector = true;
    selectedQuiz = null;
    game = null;
    players = [];
    gameState = 'lobby' as GameState;
    isConnected = false;
  }
</script>

<div class="min-h-screen bg-gray-50">
  {#if isLoading}
    <div class="min-h-screen flex items-center justify-center">
      <LoadingSpinner size="lg" />
    </div>
  {:else if showQuizSelector}
    <!-- Quiz Selection -->
    <div class="container mx-auto px-4 py-8">
      <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8 text-center">Select a Quiz to Host</h1>
        
        {#if error}
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        {/if}

        {#if quizzes.length === 0}
          <div class="text-center py-12">
            <p class="text-gray-600 mb-4">No quizzes available</p>
            <button 
              class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              onclick={() => push('/quizzes')}
            >
              Create a Quiz
            </button>
          </div>
        {:else}
          <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {#each quizzes as quiz (quiz.id)}
              <div class="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">{quiz.name}</h3>
                <p class="text-gray-600 mb-4">{quiz.questions?.length || 0} questions</p>
                <button 
                  class="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
                  onclick={() => selectQuiz(quiz)}
                >
                  Host This Quiz
                </button>
              </div>
            {/each}
          </div>
        {/if}

        <div class="text-center mt-8">
          <button 
            class="text-gray-600 hover:text-gray-800"
            onclick={() => push('/')}
          >
            ← Back to Home
          </button>
        </div>
      </div>
    </div>
  {:else}
    <!-- Game Host Interface -->
    <div class="min-h-screen">
      <!-- Header -->
      <div class="bg-white shadow-sm border-b p-4">
        <div class="flex justify-between items-center max-w-6xl mx-auto">
          <div>
            <h1 class="text-lg font-semibold">{selectedQuiz?.name || 'Quiz Game'}</h1>
            <p class="text-sm text-gray-600">
              Game Code: <span class="font-mono font-bold">{game?.code || 'Loading...'}</span>
            </p>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-600">{players.length} players</span>
            <button 
              class="text-sm text-red-600 hover:text-red-800"
              onclick={backToQuizSelection}
            >
              End Game
            </button>
          </div>
        </div>
      </div>

      <!-- Game Content -->
      <div class="max-w-6xl mx-auto p-4">
        {#if gameState === 'lobby'}
          <HostLobbyView 
            gameCode={game?.code || ''}
            {players}
            canStart={players.length > 0 && (selectedQuiz?.questions?.length || 0) > 0}
            onStart={startGame}
          />
        {:else if gameState === 'play' && currentQuestion}
          <HostGameView 
            question={currentQuestion}
            questionIndex={currentQuestionIndex}
            totalQuestions={selectedQuiz?.questions?.length || 0}
            {players}
            onShowQuestion={showQuestion}
            onRevealAnswers={revealAnswers}
            onNextQuestion={nextQuestion}
            onEndGame={endGame}
          />
        {:else if gameState === 'reveal' && currentQuestion}
          <HostResultsView 
            question={currentQuestion}
            questionIndex={currentQuestionIndex}
            totalQuestions={selectedQuiz?.questions?.length || 0}
            {players}
            onNextQuestion={nextQuestion}
            onEndGame={endGame}
          />
        {:else if gameState === 'end'}
          <div class="text-center py-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Game Complete!</h2>
            <p class="text-lg text-gray-600 mb-6">Thank you for playing</p>
            <button 
              class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              onclick={backToQuizSelection}
            >
              Host Another Game
            </button>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>
