<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { push } from 'svelte-spa-router';
  import { webSocketService } from '../services/websocket';
  import { apiService } from '../services/api';
  import type { Quiz, QuizQuestion, Player, GameState, Packet } from '../types/quiz';
  import { PacketTypes } from '../types/quiz';
  import PlayerLobbyView from '../components/PlayerLobbyView.svelte';
  import PlayerGameView from '../components/PlayerGameView.svelte';
  import PlayerResultsView from '../components/PlayerResultsView.svelte';
  import LoadingSpinner from '../components/LoadingSpinner.svelte';

  // Get URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const gameCodeParam = urlParams.get('code');
  const playerNameParam = urlParams.get('name');

  // Svelte 5 runes for state management
  let gameState = $state<GameState>('lobby' as GameState);
  let currentQuestion = $state<QuizQuestion | null>(null);
  let players = $state<Player[]>([]);
  let gameCode = $state(gameCodeParam || '');
  let playerName = $state(playerNameParam || '');
  let playerId = $state<string | null>(null);
  let gameId = $state<string | null>(null);
  let isConnected = $state(false);
  let isConnecting = $state(false);
  let error = $state<string | null>(null);
  let currentQuestionIndex = $state(0);
  let totalQuestions = $state(0);
  let playerPoints = $state(0);
  let timeRemaining = $state(0);
  let hasAnswered = $state(false);
  let selectedAnswer = $state<string | null>(null);

  onMount(async () => {
    if (gameCodeParam && playerNameParam) {
      await connectToGame();
    }
  });

  onDestroy(() => {
    webSocketService.disconnect();
  });

  async function connectToGame() {
    if (!gameCode.trim() || !playerName.trim()) {
      error = 'Game code and player name are required';
      return;
    }

    isConnecting = true;
    error = null;

    try {
      // Connect to WebSocket
      await webSocketService.connect();
      
      // Set up packet handler
      webSocketService.onPacket(handlePacket);
      
      // Send connect packet
      webSocketService.sendConnect(gameCode, playerName);
      
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to connect to game';
      isConnecting = false;
    }
  }

  function handlePacket(packet: Packet) {
    console.log('Received packet:', packet);

    switch (packet.id) {
      case PacketTypes.PlayerJoin:
        const joinPacket = packet as any;
        if (joinPacket.player) {
          // This is our own join confirmation
          playerId = joinPacket.player.id;
          isConnected = true;
          isConnecting = false;
          gameState = 'lobby' as GameState;
        }
        break;

      case PacketTypes.ChangeGameState:
        const statePacket = packet as any;
        const stateMap = {
          0: 'lobby',
          1: 'play',
          2: 'intermission',
          3: 'reveal',
          4: 'end'
        };
        gameState = stateMap[statePacket.state] as GameState || 'lobby' as GameState;
        
        if (gameState === 'play') {
          hasAnswered = false;
          selectedAnswer = null;
        }
        break;

      case PacketTypes.QuestionShow:
        const questionPacket = packet as any;
        currentQuestion = questionPacket.question;
        hasAnswered = false;
        selectedAnswer = null;
        timeRemaining = currentQuestion?.time || 30;
        startTimer();
        break;

      case PacketTypes.PlayerDisconnect:
        const disconnectPacket = packet as any;
        players = players.filter(p => p.id !== disconnectPacket.playerId);
        break;

      case PacketTypes.Answer:
        const answerPacket = packet as any;
        if (answerPacket.success) {
          hasAnswered = true;
          playerPoints += answerPacket.points || 0;
        }
        break;

      default:
        console.log('Unhandled packet type:', packet.id);
    }
  }

  function startTimer() {
    const timer = setInterval(() => {
      timeRemaining--;
      if (timeRemaining <= 0) {
        clearInterval(timer);
        if (!hasAnswered) {
          // Auto-submit or handle timeout
          handleTimeout();
        }
      }
    }, 1000);
  }

  function handleTimeout() {
    // Handle when time runs out
    hasAnswered = true;
  }

  function submitAnswer(choiceId: string) {
    if (!currentQuestion || hasAnswered) return;

    const timeTaken = (currentQuestion.time || 30) - timeRemaining;
    selectedAnswer = choiceId;
    
    webSocketService.sendAnswer(currentQuestion.id, choiceId, timeTaken);
  }

  function handleJoinGame() {
    connectToGame();
  }

  function handleLeaveGame() {
    webSocketService.disconnect();
    push('/');
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      connectToGame();
    }
  }
</script>

<div class="min-h-screen bg-gray-50">
  {#if !isConnected && !isConnecting}
    <!-- Join Game Form -->
    <div class="min-h-screen flex items-center justify-center p-4">
      <div class="bg-white rounded-xl shadow-xl p-8 w-full max-w-md">
        <h1 class="text-2xl font-bold text-center mb-6">Join Game</h1>
        
        {#if error}
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        {/if}

        <div class="space-y-4">
          <div>
            <label for="game-code" class="block text-sm font-medium text-gray-700 mb-2">
              Game Code
            </label>
            <input
              id="game-code"
              type="text"
              bind:value={gameCode}
              onkeypress={handleKeyPress}
              placeholder="Enter 6-letter code"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent uppercase text-center text-lg font-mono"
              maxlength="6"
            />
          </div>
          
          <div>
            <label for="player-name" class="block text-sm font-medium text-gray-700 mb-2">
              Your Name
            </label>
            <input
              id="player-name"
              type="text"
              bind:value={playerName}
              onkeypress={handleKeyPress}
              placeholder="Enter your name"
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              maxlength="20"
            />
          </div>
          
          <button 
            class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onclick={handleJoinGame}
            disabled={!gameCode.trim() || !playerName.trim()}
          >
            Join Game
          </button>
        </div>
        
        <div class="mt-6 text-center">
          <button 
            class="text-gray-600 hover:text-gray-800"
            onclick={() => push('/')}
          >
            ← Back to Home
          </button>
        </div>
      </div>
    </div>
  {:else if isConnecting}
    <!-- Connecting State -->
    <div class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <LoadingSpinner size="lg" />
        <p class="mt-4 text-lg text-gray-600">Connecting to game...</p>
      </div>
    </div>
  {:else}
    <!-- Game Views -->
    <div class="min-h-screen">
      <!-- Header -->
      <div class="bg-white shadow-sm border-b p-4">
        <div class="flex justify-between items-center max-w-4xl mx-auto">
          <div>
            <h1 class="text-lg font-semibold">Game: {gameCode}</h1>
            <p class="text-sm text-gray-600">Player: {playerName}</p>
          </div>
          <div class="text-right">
            <p class="text-lg font-bold text-blue-600">{playerPoints} points</p>
            <button 
              class="text-sm text-red-600 hover:text-red-800"
              onclick={handleLeaveGame}
            >
              Leave Game
            </button>
          </div>
        </div>
      </div>

      <!-- Game Content -->
      <div class="max-w-4xl mx-auto p-4">
        {#if gameState === 'lobby'}
          <PlayerLobbyView {gameCode} {players} />
        {:else if gameState === 'play' && currentQuestion}
          <PlayerGameView 
            question={currentQuestion}
            {timeRemaining}
            {hasAnswered}
            {selectedAnswer}
            onAnswer={submitAnswer}
          />
        {:else if gameState === 'reveal' && currentQuestion}
          <PlayerResultsView 
            question={currentQuestion}
            {selectedAnswer}
            {playerPoints}
          />
        {:else if gameState === 'end'}
          <div class="text-center py-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Game Over!</h2>
            <p class="text-xl text-gray-600 mb-6">Final Score: {playerPoints} points</p>
            <button 
              class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              onclick={() => push('/')}
            >
              Play Again
            </button>
          </div>
        {:else}
          <div class="text-center py-12">
            <p class="text-lg text-gray-600">Waiting for game to start...</p>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>
