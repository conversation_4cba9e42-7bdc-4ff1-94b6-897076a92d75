<script lang="ts">
  import { onMount } from 'svelte';
  import { push } from 'svelte-spa-router';
  import { apiService } from '../services/api';
  import type { Quiz, QuizQuestion, QuizChoice } from '../types/quiz';
  import LoadingSpinner from '../components/LoadingSpinner.svelte';

  export let params: { id: string } = { id: '' };

  // Svelte 5 runes for state management
  let quiz = $state<Quiz | null>(null);
  let isLoading = $state(true);
  let isSaving = $state(false);
  let error = $state<string | null>(null);
  let selectedQuestionIndex = $state<number | null>(null);
  let showDeleteConfirm = $state<string | null>(null);

  onMount(async () => {
    if (params.id) {
      await loadQuiz(params.id);
    } else {
      // Create new quiz
      quiz = {
        id: '',
        name: 'New Quiz',
        created_at: '',
        updated_at: '',
        questions: []
      };
      isLoading = false;
    }
  });

  async function loadQuiz(id: string) {
    isLoading = true;
    try {
      quiz = await apiService.getQuizById(id);
      if (!quiz) {
        error = 'Quiz not found';
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load quiz';
    } finally {
      isLoading = false;
    }
  }

  async function saveQuiz() {
    if (!quiz) return;

    isSaving = true;
    error = null;

    try {
      const savedQuiz = await apiService.saveQuiz(quiz);
      if (savedQuiz) {
        quiz = savedQuiz;
        // Show success message or redirect
        push('/quizzes');
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to save quiz';
    } finally {
      isSaving = false;
    }
  }

  function addQuestion() {
    if (!quiz) return;

    const newQuestion: QuizQuestion = {
      id: `temp-${Date.now()}`,
      name: 'New Question',
      time: 30,
      order: quiz.questions.length,
      choices: [
        { id: `temp-choice-${Date.now()}-1`, name: 'Option A', correct: true },
        { id: `temp-choice-${Date.now()}-2`, name: 'Option B', correct: false },
        { id: `temp-choice-${Date.now()}-3`, name: 'Option C', correct: false },
        { id: `temp-choice-${Date.now()}-4`, name: 'Option D', correct: false }
      ]
    };

    quiz.questions = [...quiz.questions, newQuestion];
    selectedQuestionIndex = quiz.questions.length - 1;
  }

  function deleteQuestion(index: number) {
    if (!quiz) return;

    quiz.questions = quiz.questions.filter((_, i) => i !== index);
    
    // Update order for remaining questions
    quiz.questions.forEach((q, i) => {
      q.order = i;
    });

    if (selectedQuestionIndex === index) {
      selectedQuestionIndex = null;
    } else if (selectedQuestionIndex !== null && selectedQuestionIndex > index) {
      selectedQuestionIndex--;
    }

    showDeleteConfirm = null;
  }

  function updateQuestionChoice(questionIndex: number, choiceIndex: number, field: 'name' | 'correct', value: string | boolean) {
    if (!quiz) return;

    const question = quiz.questions[questionIndex];
    if (!question) return;

    if (field === 'correct' && value === true) {
      // Only one choice can be correct
      question.choices.forEach((choice, i) => {
        choice.correct = i === choiceIndex;
      });
    } else {
      question.choices[choiceIndex][field] = value as any;
    }

    // Trigger reactivity
    quiz.questions = [...quiz.questions];
  }

  function moveQuestion(index: number, direction: 'up' | 'down') {
    if (!quiz) return;

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    if (newIndex < 0 || newIndex >= quiz.questions.length) return;

    const questions = [...quiz.questions];
    [questions[index], questions[newIndex]] = [questions[newIndex], questions[index]];
    
    // Update order
    questions.forEach((q, i) => {
      q.order = i;
    });

    quiz.questions = questions;

    // Update selected index
    if (selectedQuestionIndex === index) {
      selectedQuestionIndex = newIndex;
    } else if (selectedQuestionIndex === newIndex) {
      selectedQuestionIndex = index;
    }
  }
</script>

<div class="min-h-screen bg-gray-50">
  {#if isLoading}
    <div class="min-h-screen flex items-center justify-center">
      <LoadingSpinner size="lg" />
    </div>
  {:else if !quiz}
    <div class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <p class="text-xl text-gray-600 mb-4">Quiz not found</p>
        <button 
          class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          onclick={() => push('/quizzes')}
        >
          Back to Quizzes
        </button>
      </div>
    </div>
  {:else}
    <!-- Header -->
    <div class="bg-white shadow-sm border-b p-4">
      <div class="max-w-6xl mx-auto flex justify-between items-center">
        <div>
          <input
            type="text"
            bind:value={quiz.name}
            class="text-2xl font-bold text-gray-900 bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-blue-500 rounded px-2"
            placeholder="Quiz Name"
          />
          <p class="text-sm text-gray-600">{quiz.questions.length} questions</p>
        </div>
        <div class="flex items-center space-x-3">
          <button 
            class="text-gray-600 hover:text-gray-800 px-4 py-2"
            onclick={() => push('/quizzes')}
          >
            Cancel
          </button>
          <button 
            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onclick={saveQuiz}
            disabled={isSaving || !quiz.name.trim()}
          >
            {isSaving ? 'Saving...' : 'Save Quiz'}
          </button>
        </div>
      </div>
    </div>

    {#if error}
      <div class="max-w-6xl mx-auto px-4 py-4">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    {/if}

    <div class="max-w-6xl mx-auto p-4">
      <div class="grid lg:grid-cols-3 gap-6">
        <!-- Questions List -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-semibold text-gray-900">Questions</h3>
              <button 
                class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                onclick={addQuestion}
              >
                Add Question
              </button>
            </div>

            {#if quiz.questions.length === 0}
              <div class="text-center py-8">
                <p class="text-gray-600 mb-4">No questions yet</p>
                <button 
                  class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  onclick={addQuestion}
                >
                  Add First Question
                </button>
              </div>
            {:else}
              <div class="space-y-2">
                {#each quiz.questions as question, index (question.id)}
                  <div 
                    class="p-3 rounded-lg border cursor-pointer transition-colors {
                      selectedQuestionIndex === index 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }"
                    onclick={() => selectedQuestionIndex = index}
                  >
                    <div class="flex justify-between items-start">
                      <div class="flex-1">
                        <p class="font-medium text-gray-900 text-sm truncate">
                          {index + 1}. {question.name}
                        </p>
                        <p class="text-xs text-gray-600">{question.time}s</p>
                      </div>
                      <div class="flex items-center space-x-1 ml-2">
                        <button 
                          class="text-gray-400 hover:text-gray-600 p-1"
                          onclick={(e) => {
                            e.stopPropagation();
                            moveQuestion(index, 'up');
                          }}
                          disabled={index === 0}
                        >
                          ↑
                        </button>
                        <button 
                          class="text-gray-400 hover:text-gray-600 p-1"
                          onclick={(e) => {
                            e.stopPropagation();
                            moveQuestion(index, 'down');
                          }}
                          disabled={index === quiz.questions.length - 1}
                        >
                          ↓
                        </button>
                        <button 
                          class="text-red-400 hover:text-red-600 p-1"
                          onclick={(e) => {
                            e.stopPropagation();
                            showDeleteConfirm = question.id;
                          }}
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        </div>

        <!-- Question Editor -->
        <div class="lg:col-span-2">
          {#if selectedQuestionIndex !== null && quiz.questions[selectedQuestionIndex]}
            {@const question = quiz.questions[selectedQuestionIndex]}
            <div class="bg-white rounded-lg shadow-md p-6">
              <h3 class="text-lg font-semibold text-gray-900 mb-4">
                Edit Question {selectedQuestionIndex + 1}
              </h3>

              <!-- Question Text -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Question Text
                </label>
                <textarea
                  bind:value={question.name}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="3"
                  placeholder="Enter your question..."
                ></textarea>
              </div>

              <!-- Time Limit -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Time Limit (seconds)
                </label>
                <input
                  type="number"
                  bind:value={question.time}
                  min="5"
                  max="300"
                  class="w-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <!-- Answer Choices -->
              <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Answer Choices
                </label>
                <div class="space-y-3">
                  {#each question.choices as choice, choiceIndex (choice.id)}
                    <div class="flex items-center space-x-3 p-3 border rounded-lg {choice.correct ? 'border-green-500 bg-green-50' : 'border-gray-200'}">
                      <input
                        type="radio"
                        name="correct-{selectedQuestionIndex}"
                        checked={choice.correct}
                        onchange={() => updateQuestionChoice(selectedQuestionIndex, choiceIndex, 'correct', true)}
                        class="text-green-600 focus:ring-green-500"
                      />
                      <input
                        type="text"
                        bind:value={choice.name}
                        onchange={() => updateQuestionChoice(selectedQuestionIndex, choiceIndex, 'name', choice.name)}
                        class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder={`Option ${String.fromCharCode(65 + choiceIndex)}`}
                      />
                    </div>
                  {/each}
                </div>
                <p class="text-xs text-gray-600 mt-2">
                  Select the radio button next to the correct answer
                </p>
              </div>
            </div>
          {:else}
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
              <p class="text-gray-600">Select a question to edit, or add a new question to get started</p>
            </div>
          {/if}
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    {#if showDeleteConfirm}
      <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div class="bg-white rounded-lg p-6 w-full max-w-md">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Delete Question</h3>
          <p class="text-gray-600 mb-6">Are you sure you want to delete this question? This action cannot be undone.</p>
          <div class="flex justify-end space-x-3">
            <button 
              class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
              onclick={() => showDeleteConfirm = null}
            >
              Cancel
            </button>
            <button 
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              onclick={() => {
                const index = quiz?.questions.findIndex(q => q.id === showDeleteConfirm);
                if (index !== undefined && index !== -1) {
                  deleteQuestion(index);
                }
              }}
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    {/if}
  {/if}
</div>
