<script lang="ts">
    import { GameState } from "../../service/net";
    import { PlayerGame, state } from "../../service/player/player";
    import PlayerJoinView from "./PlayerJoinView.svelte";
    import PlayerLobbyView from "./PlayerLobbyView.svelte";
    import PlayerPlayView from "./PlayerPlayView.svelte";
    import PlayerRevealView from "./PlayerRevealView.svelte";

    // Using $state rune for reactive state
    const $game = new PlayerGame();
    const $active = false;

    // Function to handle join event
    function onJoin() {
        $active = true;
    }

    // Using $derived for computed values
    const $views = {
        [GameState.Lobby]: PlayerLobbyView,
        [GameState.Play]: PlayerPlayView,
        [GameState.Reveal]: PlayerRevealView,
        [GameState.Intermission]: PlayerRevealView
    };
</script>

{#if $active}
    <svelte:component this={$views[$state]} game={$game} />
{:else}
    <PlayerJoinView on:join={onJoin} game={$game} />
{/if}