@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

/* Custom styles for quiz platform */
.quiz-choice-card {
  @apply flex-1 h-48 flex items-center justify-center text-white text-xl font-bold cursor-pointer transition-all duration-200 hover:scale-105;
}

.quiz-choice-card:nth-child(1) {
  @apply bg-pink-400;
}

.quiz-choice-card:nth-child(2) {
  @apply bg-blue-400;
}

.quiz-choice-card:nth-child(3) {
  @apply bg-yellow-400;
}

.quiz-choice-card:nth-child(4) {
  @apply bg-purple-400;
}

.quiz-choice-card.correct {
  @apply bg-green-400;
}

.quiz-choice-card.incorrect {
  @apply bg-red-400;
}

.clock {
  @apply w-24 h-24 rounded-full border-4 border-gray-300 flex items-center justify-center text-2xl font-bold;
}

.game-code {
  @apply text-6xl font-bold tracking-widest text-center p-8 bg-gray-100 rounded-lg;
}

.player-list {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4;
}

.player-card {
  @apply bg-white rounded-lg p-4 shadow-md border;
}

.leaderboard {
  @apply bg-white rounded-lg p-6 shadow-lg;
}

.leaderboard-entry {
  @apply flex justify-between items-center p-3 border-b last:border-b-0;
}

.leaderboard-entry:nth-child(1) {
  @apply bg-yellow-100 font-bold;
}

.leaderboard-entry:nth-child(2) {
  @apply bg-gray-100;
}

.leaderboard-entry:nth-child(3) {
  @apply bg-orange-100;
}
