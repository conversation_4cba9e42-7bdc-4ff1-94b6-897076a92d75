export interface Quiz {
    id: string;
    name: string;
    created_at: string;
    updated_at: string;
    questions: QuizQuestion[];
}

export interface QuizQuestion {
    id: string;
    name: string;
    time: number;
    order: number;
    choices: QuizChoice[];
}

export interface QuizChoice {
    id: string;
    name: string;
    correct: boolean;
}

export interface Player {
    id: string;
    name: string;
    points: number;
    is_connected: boolean;
}

export interface Game {
    id: string;
    quiz: Quiz;
    code: string;
    created_at: string;
    is_active: boolean;
    state: GameState;
    current_question: QuizQuestion | null;
    players: Player[];
}

export interface PlayerAnswer {
    player: string;
    question: string;
    choice: string;
    time_taken: number;
    points: number;
    created_at: string;
}

export interface LeaderboardEntry {
    name: string;
    points: number;
}

export interface Leaderboard {
    entries: LeaderboardEntry[];
}

export enum GameState {
    Lobby = 'lobby',
    Play = 'play',
    Intermission = 'intermission',
    Reveal = 'reveal',
    End = 'end'
}

// Color constants for quiz choices
export const COLORS = [
    "bg-pink-400", 
    "bg-blue-400", 
    "bg-yellow-400", 
    "bg-purple-400"
];

// WebSocket packet types
export enum PacketTypes {
    Connect = 0,
    HostGame = 1,
    QuestionShow = 2,
    ChangeGameState = 3,
    PlayerJoin = 4,
    StartGame = 5,
    Tick = 6,
    Answer = 7,
    PlayerReveal = 8,
    Leaderboard = 9,
    PlayerDisconnect = 10
}

// WebSocket packet interfaces
export interface Packet {
    id: PacketTypes;
}

export interface ConnectPacket extends Packet {
    code: string;
    name: string;
}

export interface HostGamePacket extends Packet {
    quizId: string;
}

export interface QuestionShowPacket extends Packet {
    question: QuizQuestion;
}

export interface ChangeGameStatePacket extends Packet {
    state: GameState;
}

export interface PlayerJoinPacket extends Packet {
    player: Player;
}

export interface StartGamePacket extends Packet {
}

export interface TickPacket extends Packet {
    tick: number;
}

export interface AnswerPacket extends Packet {
    questionId: string;
    choiceId: string;
    timeTaken: number;
}

export interface PlayerRevealPacket extends Packet {
    points: number;
}

export interface LeaderboardPacket extends Packet {
    entries: LeaderboardEntry[];
}

export interface PlayerDisconnectPacket extends Packet {
    playerId: string;
}
