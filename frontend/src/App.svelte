<script lang="ts">
  import { onMount } from 'svelte';
  import router from 'svelte-spa-router';
  import HomeView from './views/HomeView.svelte';
  import HostView from './views/HostView.svelte';
  import PlayerView from './views/PlayerView.svelte';
  import EditQuizView from './views/EditQuizView.svelte';
  import QuizListView from './views/QuizListView.svelte';

  // Define routes using Svelte SPA Router
  const routes = {
    '/': HomeView,
    '/host': HostView,
    '/player': PlayerView,
    '/quizzes': QuizListView,
    '/edit/:id': EditQuizView,
    '/host/:gameId': HostView,
    '/player/:gameId': PlayerView,
  };

  // Application state using Svelte 5 runes
  let currentView = $state('home');
  let isLoading = $state(false);
  let error = $state<string | null>(null);

  onMount(() => {
    console.log('Quiz Platform App initialized');
  });
</script>

<main class="min-h-screen bg-gray-50">
  <nav class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <h1 class="text-xl font-bold text-gray-900">Quiz Platform</h1>
        </div>
        <div class="flex items-center space-x-4">
          <a href="#/" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
            Home
          </a>
          <a href="#/quizzes" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
            Quizzes
          </a>
          <a href="#/host" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
            Host
          </a>
          <a href="#/player" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">
            Join Game
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    {#if error}
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <strong class="font-bold">Error:</strong>
        <span class="block sm:inline">{error}</span>
        <button 
          class="float-right text-red-700 hover:text-red-900"
          onclick={() => error = null}
        >
          ×
        </button>
      </div>
    {/if}

    {#if isLoading}
      <div class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    {:else}
      <svelte:component this={router} {routes} />
    {/if}
  </div>
</main>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  :global(#app) {
    width: 100%;
    height: 100vh;
    margin: 0;
    padding: 0;
    text-align: left;
  }
</style>
