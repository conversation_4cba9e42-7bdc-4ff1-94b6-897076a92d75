import type { Quiz } from "../model/quiz";
import { state } from "svelte";

// Using Svelte 5 state for loading and error states
export class ApiService {
    // Reactive state for loading and errors
    loading = state(false);
    error = state<string | null>(null);
    
    // Base URL for API - can be configured from environment
    apiBaseUrl = "http://localhost:8000/api";

    async getQuizById(id: string): Promise<Quiz | null> {
        this.loading.set(true);
        this.error.set(null);
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/${id}/`);
            if (!response.ok) {
                throw new Error(`Failed to fetch quiz: ${response.statusText}`);
            }

            let json = await response.json();
            return json;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error.set(errorMessage);
            return null;
        } finally {
            this.loading.set(false);
        }
    }

    async getQuizzes(): Promise<Quiz[]> {
        this.loading.set(true);
        this.error.set(null);
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/`);
            if (!response.ok) {
                throw new Error(`Failed to fetch quizzes: ${response.statusText}`);
            }

            let json = await response.json();
            return json;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error.set(errorMessage);
            return [];
        } finally {
            this.loading.set(false);
        }
    }

    async saveQuiz(quizId: string, quiz: Quiz): Promise<boolean> {
        this.loading.set(true);
        this.error.set(null);
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/${quizId}/`, {
                method: "PUT",
                body: JSON.stringify(quiz),
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to save quiz: ${response.statusText}`);
            }
            
            return true;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error.set(errorMessage);
            return false;
        } finally {
            this.loading.set(false);
        }
    }
    
    async createQuiz(quiz: Quiz): Promise<string | null> {
        this.loading.set(true);
        this.error.set(null);
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/`, {
                method: "POST",
                body: JSON.stringify(quiz),
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to create quiz: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result.id;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error.set(errorMessage);
            return null;
        } finally {
            this.loading.set(false);
        }
    }
}

// Create a singleton instance
export const apiService = new ApiService();