import type { Player, QuizQuestion } from "../model/quiz";
import { state, effect } from "svelte";

export enum PacketTypes {
    Connect,
    HostGame,
    QuestionShow,
    ChangeGameState,
    PlayerJoin,
    StartGame,
    Tick,
    Answer,
    PlayerReveal,
    Leaderboard,
    PlayerDisconnect
}

export enum GameState {
    Lobby,
    Play,
    Intermission,
    Reveal,
    End
}

export interface Packet {
    id: PacketTypes;
}

export interface HostGamePacket extends Packet {
    quizId: string;
}

export interface ChangeGameStatePacket extends Packet {
    state: GameState;
}

export interface PlayerJoinPacket extends Packet {
    player: Player;
}

export interface TickPacket extends Packet {
    tick: number;
}

export interface PlayerDisconnectPacket extends Packet {
    playerId: string;
}

export interface ConnectPacket extends Packet {
    code: string;
    name: string;
}

export interface QuestionShowPacket extends Packet {
    question: QuizQuestion;
}

export interface QuestionAnswerPacket extends Packet {
    question: number;
}

export interface PlayerRevealPacket extends Packet {
    points: number;
}

export interface LeaderboardEntry {
    name: string;
    points: number;
}

export interface LeaderboardPacket extends Packet {
    points: LeaderboardEntry[];
}

export class NetService {
    // Reactive state using Svelte 5 runes
    connected = state(false);
    connecting = state(false);
    error = state<string | null>(null);
    
    // WebSocket connection
    private webSocket: WebSocket | null = null;
    private packetCallbacks = new Map<PacketTypes, ((packet: any) => void)[]>();
    
    // WebSocket URL - can be configured from environment
    wsBaseUrl = "ws://localhost:8000/ws";

    constructor() {
        // Auto-reconnect using effect
        effect(() => {
            if (!this.connected() && !this.connecting()) {
                this.connect();
            }
        });
    }

    connect() {
        if (this.webSocket) {
            this.webSocket.close();
        }
        
        this.connecting.set(true);
        this.error.set(null);
        
        try {
            this.webSocket = new WebSocket(this.wsBaseUrl);
            
            this.webSocket.onopen = () => {
                console.log("WebSocket connection established");
                this.connected.set(true);
                this.connecting.set(false);
            };
            
            this.webSocket.onclose = () => {
                console.log("WebSocket connection closed");
                this.connected.set(false);
                
                // Schedule reconnection after a delay
                setTimeout(() => {
                    if (!this.connected() && !this.connecting()) {
                        this.connect();
                    }
                }, 3000);
            };
            
            this.webSocket.onerror = (event) => {
                console.error("WebSocket error:", event);
                this.error.set("WebSocket connection error");
                this.connected.set(false);
                this.connecting.set(false);
            };
            
            this.webSocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    const packetId = data.id;
                    
                    console.log("Received packet:", data);
                    
                    // Call registered callbacks for this packet type
                    const callbacks = this.packetCallbacks.get(packetId) || [];
                    callbacks.forEach(callback => callback(data));
                } catch (err) {
                    console.error("Error processing message:", err);
                }
            };
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error.set(errorMessage);
            this.connecting.set(false);
        }
    }

    onPacket(packetType: PacketTypes, callback: (packet: any) => void) {
        const callbacks = this.packetCallbacks.get(packetType) || [];
        callbacks.push(callback);
        this.packetCallbacks.set(packetType, callbacks);
        
        // Return unsubscribe function
        return () => {
            const callbacks = this.packetCallbacks.get(packetType) || [];
            const index = callbacks.indexOf(callback);
            if (index !== -1) {
                callbacks.splice(index, 1);
                this.packetCallbacks.set(packetType, callbacks);
            }
        };
    }

    sendPacket(packet: Packet) {
        if (!this.webSocket || this.webSocket.readyState !== WebSocket.OPEN) {
            console.error("Cannot send packet: WebSocket not connected");
            return;
        }
        
        try {
            // Send packet as JSON
            this.webSocket.send(JSON.stringify(packet));
        } catch (err) {
            console.error("Error sending packet:", err);
        }
    }
    
    disconnect() {
        if (this.webSocket) {
            this.webSocket.close();
            this.webSocket = null;
        }
        this.connected.set(false);
    }
}

// Create a singleton instance
export const netService = new NetService();