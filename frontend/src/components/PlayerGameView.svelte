<script lang="ts">
  import type { QuizQuestion } from '../types/quiz';
  import { COLORS } from '../types/quiz';

  interface Props {
    question: QuizQuestion;
    timeRemaining: number;
    hasAnswered: boolean;
    selectedAnswer: string | null;
    onAnswer: (choiceId: string) => void;
  }

  let { question, timeRemaining, hasAnswered, selectedAnswer, onAnswer }: Props = $props();

  function handleChoiceClick(choiceId: string) {
    if (!hasAnswered) {
      onAnswer(choiceId);
    }
  }

  function getChoiceClass(choiceIndex: number, choiceId: string) {
    let baseClass = 'quiz-choice-card';
    
    if (hasAnswered && selectedAnswer === choiceId) {
      baseClass += ' ring-4 ring-blue-500';
    }
    
    if (hasAnswered) {
      baseClass += ' opacity-75 cursor-not-allowed';
    }
    
    return baseClass;
  }

  // Calculate progress percentage
  $: progressPercentage = ((question.time - timeRemaining) / question.time) * 100;
</script>

<div class="max-w-4xl mx-auto">
  <!-- Question Header -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-2xl font-bold text-gray-900">Question</h2>
      <div class="clock">
        {timeRemaining}
      </div>
    </div>
    
    <!-- Progress Bar -->
    <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
      <div 
        class="bg-blue-600 h-2 rounded-full transition-all duration-1000"
        style="width: {progressPercentage}%"
      ></div>
    </div>
    
    <p class="text-xl text-gray-800">{question.name}</p>
  </div>

  <!-- Answer Status -->
  {#if hasAnswered}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 text-center">
      <p class="font-semibold">Answer submitted!</p>
      <p class="text-sm">Waiting for other players...</p>
    </div>
  {:else if timeRemaining <= 5}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 text-center">
      <p class="font-semibold">Time running out!</p>
    </div>
  {/if}

  <!-- Answer Choices -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    {#each question.choices as choice, index (choice.id)}
      <button
        class={getChoiceClass(index, choice.id)}
        onclick={() => handleChoiceClick(choice.id)}
        disabled={hasAnswered}
        style="background-color: {hasAnswered && selectedAnswer === choice.id ? '#3B82F6' : ''}"
      >
        <div class="flex items-center justify-center h-full p-6">
          <span class="text-xl font-bold text-center">{choice.name}</span>
        </div>
      </button>
    {/each}
  </div>

  <!-- Instructions -->
  {#if !hasAnswered}
    <div class="text-center mt-6">
      <p class="text-gray-600">Tap an answer to submit your choice</p>
    </div>
  {/if}
</div>
