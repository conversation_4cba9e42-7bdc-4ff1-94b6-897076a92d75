<script lang="ts">
  import type { QuizQuestion } from '../types/quiz';

  interface Props {
    question: QuizQuestion;
    selectedAnswer: string | null;
    playerPoints: number;
  }

  let { question, selectedAnswer, playerPoints }: Props = $props();

  // Find the correct answer
  $: correctChoice = question.choices.find(choice => choice.correct);
  $: selectedChoice = selectedAnswer ? question.choices.find(choice => choice.id === selectedAnswer) : null;
  $: isCorrect = selectedChoice?.correct || false;
</script>

<div class="max-w-4xl mx-auto text-center">
  <!-- Result Header -->
  <div class="mb-8">
    {#if isCorrect}
      <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-green-600 mb-2">Correct!</h2>
    {:else}
      <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-red-600 mb-2">Incorrect</h2>
    {/if}
  </div>

  <!-- Question and Answer -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{question.name}</h3>
    
    <div class="space-y-4">
      <!-- Show all choices with correct/incorrect indicators -->
      {#each question.choices as choice (choice.id)}
        <div class="p-4 rounded-lg border-2 {
          choice.correct 
            ? 'border-green-500 bg-green-50' 
            : selectedAnswer === choice.id 
              ? 'border-red-500 bg-red-50' 
              : 'border-gray-200 bg-gray-50'
        }">
          <div class="flex items-center justify-between">
            <span class="text-lg">{choice.name}</span>
            <div class="flex items-center space-x-2">
              {#if choice.correct}
                <span class="text-green-600 font-semibold">Correct</span>
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              {:else if selectedAnswer === choice.id}
                <span class="text-red-600 font-semibold">Your answer</span>
                <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              {/if}
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>

  <!-- Score Display -->
  <div class="bg-blue-50 rounded-lg p-6">
    <h4 class="text-lg font-semibold text-gray-900 mb-2">Your Score</h4>
    <p class="text-3xl font-bold text-blue-600">{playerPoints} points</p>
  </div>

  <!-- Waiting Message -->
  <div class="mt-6">
    <p class="text-gray-600">Waiting for next question...</p>
    <div class="flex justify-center mt-4">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
    </div>
  </div>
</div>
