<script lang="ts">
  import type { Player } from '../types/quiz';

  interface Props {
    gameCode: string;
    players: Player[];
  }

  let { gameCode, players }: Props = $props();
</script>

<div class="text-center py-8">
  <!-- Game Code Display -->
  <div class="mb-8">
    <h2 class="text-2xl font-semibold text-gray-700 mb-4">Game Code</h2>
    <div class="game-code text-gray-800 mx-auto max-w-md">
      {gameCode}
    </div>
    <p class="text-gray-600 mt-4">Share this code with other players</p>
  </div>

  <!-- Waiting Message -->
  <div class="mb-8">
    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
      <svg class="w-8 h-8 text-blue-600 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
    <h3 class="text-xl font-semibold text-gray-900 mb-2">Waiting for game to start...</h3>
    <p class="text-gray-600">The host will begin the quiz shortly</p>
  </div>

  <!-- Players List -->
  {#if players.length > 0}
    <div class="max-w-2xl mx-auto">
      <h4 class="text-lg font-semibold text-gray-900 mb-4">
        Players ({players.length})
      </h4>
      <div class="player-list">
        {#each players as player (player.id)}
          <div class="player-card">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-3">
                {player.name.charAt(0).toUpperCase()}
              </div>
              <div>
                <p class="font-medium text-gray-900">{player.name}</p>
                <p class="text-sm text-gray-600">
                  {player.is_connected ? 'Connected' : 'Disconnected'}
                </p>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {:else}
    <div class="text-gray-600">
      <p>No other players have joined yet</p>
    </div>
  {/if}
</div>
