<script lang="ts">
  import type { Quiz } from '../types/quiz';

  interface Props {
    quiz: Quiz;
    onEdit?: () => void;
    onDelete?: () => void;
    onHost?: () => void;
  }

  let { quiz, onEdit, onDelete, onHost }: Props = $props();

  function formatDate(dateString: string) {
    return new Date(dateString).toLocaleDateString();
  }
</script>

<div class="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow">
  <div class="flex justify-between items-start mb-4">
    <h3 class="text-xl font-semibold text-gray-900 truncate">{quiz.name}</h3>
    <div class="flex space-x-2">
      {#if onEdit}
        <button 
          class="text-blue-600 hover:text-blue-800 p-1"
          onclick={onEdit}
          title="Edit Quiz"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
        </button>
      {/if}
      
      {#if onDelete}
        <button 
          class="text-red-600 hover:text-red-800 p-1"
          onclick={onDelete}
          title="Delete Quiz"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
      {/if}
    </div>
  </div>

  <div class="text-sm text-gray-600 mb-4">
    <div class="flex items-center mb-2">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <span>{quiz.questions?.length || 0} questions</span>
    </div>
    
    <div class="flex items-center">
      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <span>Created {formatDate(quiz.created_at)}</span>
    </div>
  </div>

  <div class="flex space-x-3">
    {#if onHost}
      <button 
        class="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors font-medium"
        onclick={onHost}
      >
        Host Game
      </button>
    {/if}
    
    {#if onEdit}
      <button 
        class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
        onclick={onEdit}
      >
        Edit Quiz
      </button>
    {/if}
  </div>
</div>
