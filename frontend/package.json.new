{"name": "quiz-platform-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.2", "@tsconfig/svelte": "^5.0.2", "autoprefixer": "^10.4.18", "postcss": "^8.4.35", "svelte": "^5.0.0-next.1", "svelte-check": "^3.6.4", "tailwindcss": "^3.4.1", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^5.4.19"}, "dependencies": {"svelte-spa-router": "^4.0.1"}}