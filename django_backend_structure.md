# Django Backend Structure for Quiz Platform

## Project Structure

```
quiz_platform/
├── manage.py
├── quiz_platform/
│   ├── __init__.py
│   ├── asgi.py
│   ├── settings.py
│   ├── urls.py
│   ├── wsgi.py
│   └── routing.py
├── quizzes/
│   ├── __init__.py
│   ├── admin.py
│   ├── apps.py
│   ├── consumers.py
│   ├── migrations/
│   ├── models.py
│   ├── serializers.py
│   ├── urls.py
│   └── views.py
├── games/
│   ├── __init__.py
│   ├── admin.py
│   ├── apps.py
│   ├── consumers.py
│   ├── migrations/
│   ├── models.py
│   ├── serializers.py
│   ├── urls.py
│   └── views.py
└── static/
    └── frontend/
```

## Models

### Quiz Models (quizzes/models.py)

```python
from django.db import models
import uuid

class Quiz(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name

class QuizQuestion(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quiz = models.ForeignKey(Quiz, related_name='questions', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    time = models.IntegerField(default=30)  # Time in seconds
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return self.name

class QuizChoice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    question = models.ForeignKey(QuizQuestion, related_name='choices', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    correct = models.BooleanField(default=False)
    
    def __str__(self):
        return self.name
```

### Game Models (games/models.py)

```python
from django.db import models
import uuid
from quizzes.models import Quiz, QuizQuestion, QuizChoice

class Game(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE)
    code = models.CharField(max_length=6, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    STATE_CHOICES = [
        ('lobby', 'Lobby'),
        ('play', 'Play'),
        ('intermission', 'Intermission'),
        ('reveal', 'Reveal'),
        ('end', 'End'),
    ]
    state = models.CharField(max_length=12, choices=STATE_CHOICES, default='lobby')
    current_question = models.ForeignKey(QuizQuestion, null=True, blank=True, on_delete=models.SET_NULL)
    
    def __str__(self):
        return f"Game {self.code} - {self.quiz.name}"

class Player(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    game = models.ForeignKey(Game, related_name='players', on_delete=models.CASCADE)
    name = models.CharField(max_length=50)
    points = models.IntegerField(default=0)
    is_connected = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name

class PlayerAnswer(models.Model):
    player = models.ForeignKey(Player, related_name='answers', on_delete=models.CASCADE)
    question = models.ForeignKey(QuizQuestion, on_delete=models.CASCADE)
    choice = models.ForeignKey(QuizChoice, on_delete=models.CASCADE)
    time_taken = models.FloatField()  # Time taken to answer in seconds
    points = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('player', 'question')
    
    def __str__(self):
        return f"{self.player.name}'s answer to {self.question.name}"
```

## API Endpoints

### Quiz API (quizzes/views.py)

```python
from rest_framework import viewsets
from .models import Quiz, QuizQuestion, QuizChoice
from .serializers import QuizSerializer, QuizQuestionSerializer, QuizChoiceSerializer

class QuizViewSet(viewsets.ModelViewSet):
    queryset = Quiz.objects.all()
    serializer_class = QuizSerializer
    lookup_field = 'id'
```

### Game API (games/views.py)

```python
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Game, Player
from .serializers import GameSerializer, PlayerSerializer
import random
import string

class GameViewSet(viewsets.ModelViewSet):
    queryset = Game.objects.all()
    serializer_class = GameSerializer
    lookup_field = 'id'
    
    @action(detail=False, methods=['post'])
    def create_game(self, request):
        quiz_id = request.data.get('quiz_id')
        if not quiz_id:
            return Response({'error': 'Quiz ID is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Generate a unique game code
        code = ''.join(random.choices(string.ascii_uppercase, k=6))
        while Game.objects.filter(code=code).exists():
            code = ''.join(random.choices(string.ascii_uppercase, k=6))
        
        game = Game.objects.create(quiz_id=quiz_id, code=code)
        return Response(GameSerializer(game).data)
    
    @action(detail=False, methods=['get'])
    def join_game(self, request):
        code = request.query_params.get('code')
        if not code:
            return Response({'error': 'Game code is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            game = Game.objects.get(code=code, is_active=True)
            return Response({'game_id': str(game.id)})
        except Game.DoesNotExist:
            return Response({'error': 'Game not found'}, status=status.HTTP_404_NOT_FOUND)
```

## WebSocket Consumers

### Game Consumer (games/consumers.py)

```python
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Game, Player, PlayerAnswer
from quizzes.models import Quiz, QuizQuestion, QuizChoice

class GameConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        await self.accept()
        self.game_id = None
        self.player_id = None
        self.game_group_name = None
    
    async def disconnect(self, close_code):
        if self.game_group_name:
            await self.channel_layer.group_discard(
                self.game_group_name,
                self.channel_name
            )
        
        if self.player_id:
            await self.set_player_disconnected()
            
            # Notify other players
            if self.game_group_name:
                await self.channel_layer.group_send(
                    self.game_group_name,
                    {
                        'type': 'player_disconnect',
                        'player_id': self.player_id
                    }
                )
    
    async def receive(self, text_data):
        data = json.loads(text_data)
        packet_id = data.get('id')
        
        # Handle different packet types
        if packet_id == 0:  # Connect
            await self.handle_connect(data)
        elif packet_id == 1:  # HostGame
            await self.handle_host_game(data)
        elif packet_id == 2:  # QuestionShow
            await self.handle_question_show(data)
        # ... other packet handlers
    
    async def handle_connect(self, data):
        code = data.get('code')
        name = data.get('name')
        
        game = await self.get_game_by_code(code)
        if not game:
            await self.send(json.dumps({
                'error': 'Game not found'
            }))
            return
        
        player = await self.create_player(game, name)
        self.player_id = str(player.id)
        self.game_id = str(game.id)
        self.game_group_name = f'game_{self.game_id}'
        
        # Join the game group
        await self.channel_layer.group_add(
            self.game_group_name,
            self.channel_name
        )
        
        # Notify other players
        await self.channel_layer.group_send(
            self.game_group_name,
            {
                'type': 'player_join',
                'player': {
                    'id': self.player_id,
                    'name': name
                }
            }
        )
    
    # ... other handler methods and database access methods
```

## Settings Configuration

### Django Settings (quiz_platform/settings.py)

```python
# Add channels to installed apps
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'corsheaders',
    'channels',
    'quizzes',
    'games',
]

# Configure channels
ASGI_APPLICATION = 'quiz_platform.asgi.application'
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# Configure CORS
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",  # Vite dev server
]

# Configure REST Framework
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_AUTHENTICATION_CLASSES': [],
    'DEFAULT_PERMISSION_CLASSES': [],
}
```

## ASGI Configuration

### ASGI Setup (quiz_platform/asgi.py)

```python
import os
from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
import quiz_platform.routing

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'quiz_platform.settings')

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": AuthMiddlewareStack(
        URLRouter(
            quiz_platform.routing.websocket_urlpatterns
        )
    ),
})
```

### WebSocket Routing (quiz_platform/routing.py)

```python
from django.urls import path
from games.consumers import GameConsumer

websocket_urlpatterns = [
    path('ws/', GameConsumer.as_asgi()),
]
```

## URL Configuration

### Main URLs (quiz_platform/urls.py)

```python
from django.contrib import admin
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from quizzes.views import QuizViewSet
from games.views import GameViewSet

router = DefaultRouter()
router.register(r'quizzes', QuizViewSet)
router.register(r'games', GameViewSet)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include(router.urls)),
]
```

## Requirements

```
Django==5.0.0
djangorestframework==3.14.0
channels==4.0.0
daphne==4.0.0
django-cors-headers==4.3.0
```