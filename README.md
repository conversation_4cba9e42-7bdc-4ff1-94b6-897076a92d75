# Quiz Platform - Svelte 5 + Django

A modern, real-time quiz platform built with Svelte 5 (using runes) frontend and Django backend with WebSocket support for live gameplay.

## Features

- **Real-time Gameplay**: Live quiz sessions with WebSocket communication
- **Modern Frontend**: Built with Svelte 5 using the new runes system
- **Robust Backend**: Django REST API with Django Channels for WebSocket support
- **Quiz Management**: Create, edit, and manage quizzes with multiple-choice questions
- **Live Hosting**: Host quiz games with real-time player interaction
- **Player Experience**: Join games with simple codes, answer questions, see results
- **Responsive Design**: Works on desktop and mobile devices
- **Clean Architecture**: Separation of concerns with DRY principles

## Technology Stack

### Frontend
- **Svelte 5** with runes for reactive state management
- **TypeScript** for type safety
- **Vite** for fast development and building
- **TailwindCSS** for styling
- **DaisyUI** for UI components
- **Svelte SPA Router** for client-side routing

### Backend
- **Django 5.2** for the web framework
- **Django REST Framework** for API endpoints
- **Django Channels** for WebSocket support
- **SQLite** for database (easily configurable for other databases)
- **CORS Headers** for cross-origin requests

## Project Structure

```
├── src/                          # Svelte 5 Frontend
│   ├── components/              # Reusable Svelte components
│   ├── views/                   # Page components
│   ├── services/                # API and WebSocket services
│   ├── types/                   # TypeScript type definitions
│   ├── App.svelte              # Main app component
│   └── main.ts                 # Application entry point
├── src/src/                     # Django Backend
│   ├── settings.py             # Django settings
│   ├── urls.py                 # URL routing
│   ├── asgi.py                 # ASGI configuration
│   └── routing.py              # WebSocket routing
├── src/quizzes/                 # Quiz management app
│   ├── models.py               # Quiz data models
│   ├── serializers.py          # API serializers
│   ├── views.py                # API views
│   └── management/             # Management commands
├── src/games/                   # Game session app
│   ├── models.py               # Game data models
│   ├── views.py                # Game API views
│   ├── consumers.py            # WebSocket consumers
│   └── serializers.py          # Game serializers
└── docs/                       # Documentation
```

## Quick Start

### Prerequisites
- Python 3.11+
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd quiz-platform
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Run Django migrations**
   ```bash
   python src/manage.py migrate
   ```

5. **Create sample data (optional)**
   ```bash
   python src/manage.py create_sample_data
   ```

### Development

1. **Start the Django backend**
   ```bash
   python src/manage.py runserver 8000
   ```

2. **Start the Svelte frontend** (in a new terminal)
   ```bash
   npm run dev
   ```

3. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:8000/api
   - Django Admin: http://localhost:8000/admin

## Usage

### Creating Quizzes

1. Navigate to the "Manage Quizzes" section
2. Click "Create New Quiz"
3. Add questions with multiple-choice answers
4. Mark the correct answer for each question
5. Set time limits for each question
6. Save your quiz

### Hosting a Game

1. Go to "Host" section
2. Select a quiz to host
3. Share the generated game code with players
4. Wait for players to join
5. Start the game and control the flow
6. View real-time results and leaderboards

### Joining a Game

1. Go to "Join Game" section
2. Enter the game code provided by the host
3. Enter your name
4. Wait for the game to start
5. Answer questions within the time limit
6. View your results and final score

## API Documentation

### Quiz Endpoints

- `GET /api/quizzes/` - List all quizzes
- `POST /api/quizzes/` - Create a new quiz
- `GET /api/quizzes/{id}/` - Get quiz details
- `PUT /api/quizzes/{id}/` - Update a quiz
- `DELETE /api/quizzes/{id}/` - Delete a quiz

### Game Endpoints

- `POST /api/games/create_game/` - Create a new game session
- `POST /api/games/join_game/` - Join a game with code and name
- `GET /api/games/{id}/players/` - Get game players
- `GET /api/games/{id}/leaderboard/` - Get game leaderboard

### WebSocket Events

The WebSocket connection handles real-time game events:

- **Connect**: Player joins a game
- **HostGame**: Host starts a new game
- **QuestionShow**: Display a question to players
- **ChangeGameState**: Update game state (lobby, play, reveal, end)
- **PlayerJoin**: New player joins the game
- **StartGame**: Game begins
- **Answer**: Player submits an answer
- **PlayerReveal**: Show answer results
- **Leaderboard**: Update leaderboard
- **PlayerDisconnect**: Player leaves the game

## Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///db.sqlite3
CORS_ALLOWED_ORIGINS=http://localhost:5173
```

### Database Configuration

The project uses SQLite by default. To use PostgreSQL or MySQL:

1. Install the appropriate database adapter
2. Update the `DATABASES` setting in `src/src/settings.py`
3. Run migrations: `python src/manage.py migrate`

## Deployment

### Frontend (Vite Build)

```bash
npm run build
```

The built files will be in the `dist/` directory.

### Backend (Django)

1. Set `DEBUG=False` in settings
2. Configure a production database
3. Set up a production ASGI server (e.g., Daphne, Uvicorn)
4. Configure static file serving
5. Set up proper CORS and security settings

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Testing

### Running Tests

```bash
# Backend tests
python src/manage.py test

# Frontend tests (if implemented)
npm run test
```

### Manual Testing

1. Create a quiz with multiple questions
2. Host a game and generate a code
3. Join the game from multiple browser tabs/devices
4. Test the complete game flow
5. Verify real-time updates work correctly

## Troubleshooting

### Common Issues

1. **WebSocket connection fails**
   - Check that Django Channels is properly configured
   - Verify ASGI server is running
   - Check firewall settings

2. **CORS errors**
   - Ensure `django-cors-headers` is installed
   - Check `CORS_ALLOWED_ORIGINS` in settings
   - Verify frontend URL is whitelisted

3. **Frontend build fails**
   - Check Node.js version compatibility
   - Clear node_modules and reinstall
   - Verify all dependencies are installed

4. **Database errors**
   - Run migrations: `python src/manage.py migrate`
   - Check database permissions
   - Verify database configuration

## Performance Considerations

- Use Redis for WebSocket channel layers in production
- Implement database connection pooling
- Add caching for frequently accessed data
- Optimize frontend bundle size
- Use CDN for static assets

## Security Notes

- Change default SECRET_KEY in production
- Use HTTPS in production
- Implement rate limiting for API endpoints
- Validate all user inputs
- Use environment variables for sensitive data

## Acknowledgments

- Built with modern web technologies
- Inspired by interactive quiz platforms like Kahoot
- Uses clean architecture principles
- Follows Django and Svelte best practices
