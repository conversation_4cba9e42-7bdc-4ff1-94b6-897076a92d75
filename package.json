{"name": "quiz-platform-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^5.0.0", "@tsconfig/svelte": "^5.0.4", "@types/node": "^22.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-spa-router": "^4.0.1", "typescript": "^5.6.0", "vite": "^6.3.5"}, "dependencies": {"@tailwindcss/cli": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "tailwindcss": "^4.1.11"}}