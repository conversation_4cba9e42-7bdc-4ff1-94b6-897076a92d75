<script lang="ts">
  import { onMount } from 'svelte';
  import router from 'svelte-spa-router';
  import HomeView from './views/HomeView.svelte';
  import HostView from './views/HostView.svelte';
  import PlayerView from './views/PlayerView.svelte';
  import EditQuizView from './views/EditQuizView.svelte';
  import QuizListView from './views/QuizListView.svelte';

  // Define routes using Svelte SPA Router
  const routes = {
    '/': HomeView,
    '/host': HostView,
    '/player': PlayerView,
    '/quizzes': QuizListView,
    '/edit/:id': EditQuizView,
    '/host/:gameId': HostView,
    '/player/:gameId': PlayerView,
  };

  // Application state using Svelte 5 runes
  let currentView = $state('home');
  let isLoading = $state(false);
  let error = $state<string | null>(null);

  onMount(() => {
    console.log('Quiz Platform App initialized');
  });
</script>

<main class="app-main">
  <nav class="app-nav">
    <div class="nav-container">
      <div class="nav-content">
        <div class="nav-brand">
          <h1 class="app-title">Quiz Platform</h1>
        </div>
        <div class="nav-links">
          <a href="#/" class="nav-link">
            Home
          </a>
          <a href="#/quizzes" class="nav-link">
            Quizzes
          </a>
          <a href="#/host" class="nav-link">
            Host
          </a>
          <a href="#/player" class="nav-link">
            Join Game
          </a>
        </div>
      </div>
    </div>
  </nav>

  <div class="main-content">
    {#if error}
      <div class="error-banner">
        <strong class="error-title">Error:</strong>
        <span class="error-message">{error}</span>
        <button
          class="error-close"
          onclick={() => error = null}
        >
          ×
        </button>
      </div>
    {/if}

    {#if isLoading}
      <div class="loading-container">
        <div class="loading-spinner"></div>
      </div>
    {:else}
      {#snippet routerComponent()}
        <svelte:component this={router} {routes} />
      {/snippet}
      {@render routerComponent()}
    {/if}
  </div>
</main>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  :global(#app) {
    width: 100%;
    height: 100vh;
    margin: 0;
    padding: 0;
    text-align: left;
  }
</style>
