<script lang="ts">
  import type { QuizQuestion, Player } from '../types/quiz';
  import { COLORS } from '../types/quiz';

  interface Props {
    question: QuizQuestion;
    questionIndex: number;
    totalQuestions: number;
    players: Player[];
    onShowQuestion: () => void;
    onRevealAnswers: () => void;
    onNextQuestion: () => void;
    onEndGame: () => void;
  }

  let { 
    question, 
    questionIndex, 
    totalQuestions, 
    players, 
    onShowQuestion, 
    onRevealAnswers, 
    onNextQuestion, 
    onEndGame 
  }: Props = $props();

  let timeRemaining = $state(question.time);
  let timerActive = $state(false);
  let showingQuestion = $state(false);
  let showingAnswers = $state(false);

  function startTimer() {
    if (timerActive) return;
    
    timerActive = true;
    showingQuestion = true;
    timeRemaining = question.time;
    
    const timer = setInterval(() => {
      timeRemaining--;
      if (timeRemaining <= 0) {
        clearInterval(timer);
        timerActive = false;
        // Auto-reveal answers when time is up
        revealAnswers();
      }
    }, 1000);
  }

  function showQuestion() {
    onShowQuestion();
    startTimer();
  }

  function revealAnswers() {
    showingAnswers = true;
    timerActive = false;
    onRevealAnswers();
  }

  function nextQuestion() {
    // Reset state for next question
    showingQuestion = false;
    showingAnswers = false;
    timerActive = false;
    onNextQuestion();
  }

  function endGame() {
    onEndGame();
  }

  // Calculate progress
  $: progress = ((questionIndex + 1) / totalQuestions) * 100;
  $: timeProgress = question.time > 0 ? ((question.time - timeRemaining) / question.time) * 100 : 0;
</script>

<div class="max-w-6xl mx-auto">
  <!-- Question Progress -->
  <div class="bg-white rounded-lg shadow-md p-4 mb-6">
    <div class="flex justify-between items-center mb-2">
      <span class="text-sm font-medium text-gray-700">
        Question {questionIndex + 1} of {totalQuestions}
      </span>
      <span class="text-sm text-gray-600">{players.length} players</span>
    </div>
    <div class="w-full bg-gray-200 rounded-full h-2">
      <div 
        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
        style="width: {progress}%"
      ></div>
    </div>
  </div>

  <div class="grid lg:grid-cols-3 gap-6">
    <!-- Question Display -->
    <div class="lg:col-span-2">
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-2xl font-bold text-gray-900">Current Question</h2>
          {#if timerActive}
            <div class="clock">
              {timeRemaining}
            </div>
          {/if}
        </div>
        
        {#if timerActive}
          <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div 
              class="bg-red-500 h-2 rounded-full transition-all duration-1000"
              style="width: {timeProgress}%"
            ></div>
          </div>
        {/if}
        
        <p class="text-xl text-gray-800 mb-6">{question.name}</p>

        <!-- Answer Choices -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {#each question.choices as choice, index (choice.id)}
            <div 
              class="quiz-choice-card {showingAnswers && choice.correct ? 'correct' : ''}"
              style="background-color: {COLORS[index] ? COLORS[index].replace('bg-', '#') : '#6B7280'}"
            >
              <div class="flex items-center justify-center h-full p-4">
                <span class="text-xl font-bold text-center text-white">{choice.name}</span>
                {#if showingAnswers && choice.correct}
                  <svg class="w-8 h-8 text-white ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                {/if}
              </div>
            </div>
          {/each}
        </div>
      </div>

      <!-- Host Controls -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Game Controls</h3>
        <div class="flex flex-wrap gap-3">
          {#if !showingQuestion}
            <button 
              class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              onclick={showQuestion}
            >
              Show Question
            </button>
          {:else if !showingAnswers}
            <button 
              class="bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
              onclick={revealAnswers}
            >
              Reveal Answers
            </button>
          {:else}
            {#if questionIndex < totalQuestions - 1}
              <button 
                class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
                onclick={nextQuestion}
              >
                Next Question
              </button>
            {:else}
              <button 
                class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                onclick={endGame}
              >
                End Game
              </button>
            {/if}
          {/if}
          
          <button 
            class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
            onclick={endGame}
          >
            End Game Early
          </button>
        </div>
      </div>
    </div>

    <!-- Players Panel -->
    <div class="lg:col-span-1">
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Players ({players.length})</h3>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          {#each players.sort((a, b) => b.points - a.points) as player, index (player.id)}
            <div class="flex items-center justify-between p-3 rounded-lg {index === 0 ? 'bg-yellow-50 border border-yellow-200' : 'bg-gray-50'}">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-semibold mr-3">
                  {index + 1}
                </div>
                <div>
                  <p class="font-medium text-gray-900 text-sm">{player.name}</p>
                  <p class="text-xs text-gray-600">
                    {player.is_connected ? 'Connected' : 'Disconnected'}
                  </p>
                </div>
              </div>
              <div class="text-right">
                <p class="font-bold text-blue-600">{player.points}</p>
                <p class="text-xs text-gray-500">pts</p>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
  </div>
</div>
