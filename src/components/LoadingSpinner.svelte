<script lang="ts">
  interface Props {
    size?: 'sm' | 'md' | 'lg';
    color?: string;
  }

  let { size = 'md', color = 'blue-600' }: Props = $props();

  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-12 w-12',
    lg: 'h-16 w-16'
  };
</script>

<div class="flex justify-center items-center py-8">
  <div class="animate-spin rounded-full {sizeClasses[size]} border-b-2 border-{color}"></div>
</div>
