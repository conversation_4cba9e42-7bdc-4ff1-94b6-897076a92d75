<script lang="ts">
  import type { Player } from '../types/quiz';

  interface Props {
    gameCode: string;
    players: Player[];
    canStart: boolean;
    onStart: () => void;
  }

  let { gameCode, players, canStart, onStart }: Props = $props();
</script>

<div class="max-w-4xl mx-auto">
  <!-- Game Code Display -->
  <div class="text-center mb-8">
    <h2 class="text-2xl font-semibold text-gray-700 mb-4">Game Code</h2>
    <div class="game-code text-gray-800 mx-auto max-w-md">
      {gameCode}
    </div>
    <p class="text-gray-600 mt-4">Players can join using this code</p>
  </div>

  <!-- Players Section -->
  <div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-xl font-semibold text-gray-900">
        Players ({players.length})
      </h3>
      {#if players.length > 0}
        <button 
          class="text-sm text-blue-600 hover:text-blue-800"
          onclick={() => {/* Refresh players list */}}
        >
          Refresh
        </button>
      {/if}
    </div>

    {#if players.length === 0}
      <div class="text-center py-8">
        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
        <p class="text-gray-600">Waiting for players to join...</p>
        <p class="text-sm text-gray-500 mt-2">Share the game code above</p>
      </div>
    {:else}
      <div class="player-list">
        {#each players as player (player.id)}
          <div class="player-card">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold mr-3">
                  {player.name.charAt(0).toUpperCase()}
                </div>
                <div>
                  <p class="font-medium text-gray-900">{player.name}</p>
                  <p class="text-sm text-gray-600">
                    {player.is_connected ? 'Connected' : 'Disconnected'}
                  </p>
                </div>
              </div>
              <div class="flex items-center">
                <span class="text-sm text-gray-500">{player.points} pts</span>
                <div class="ml-3 w-3 h-3 rounded-full {player.is_connected ? 'bg-green-400' : 'bg-red-400'}"></div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {/if}
  </div>

  <!-- Start Game Section -->
  <div class="text-center">
    {#if !canStart}
      <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
        <p class="font-semibold">Cannot start game yet</p>
        <p class="text-sm">
          {#if players.length === 0}
            Waiting for at least one player to join
          {:else}
            Quiz must have at least one question
          {/if}
        </p>
      </div>
    {/if}

    <button 
      class="bg-green-600 text-white px-8 py-4 rounded-lg text-xl font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      onclick={onStart}
      disabled={!canStart}
    >
      Start Game
    </button>

    {#if canStart}
      <p class="text-sm text-gray-600 mt-2">
        Ready to start with {players.length} player{players.length !== 1 ? 's' : ''}
      </p>
    {/if}
  </div>

  <!-- Instructions -->
  <div class="mt-8 bg-blue-50 rounded-lg p-4">
    <h4 class="font-semibold text-blue-900 mb-2">Host Instructions</h4>
    <ul class="text-sm text-blue-800 space-y-1">
      <li>• Share the game code with players</li>
      <li>• Wait for players to join</li>
      <li>• Click "Start Game" when ready</li>
      <li>• Control the game flow during play</li>
    </ul>
  </div>
</div>
