<script lang="ts">
  import type { QuizQuestion, Player } from '../types/quiz';

  interface Props {
    question: QuizQuestion;
    questionIndex: number;
    totalQuestions: number;
    players: Player[];
    onNextQuestion: () => void;
    onEndGame: () => void;
  }

  let { 
    question, 
    questionIndex, 
    totalQuestions, 
    players, 
    onNextQuestion, 
    onEndGame 
  }: Props = $props();

  // Find the correct answer
  $: correctChoice = question.choices.find(choice => choice.correct);
  
  // Sort players by points for leaderboard
  $: sortedPlayers = [...players].sort((a, b) => b.points - a.points);
  
  // Calculate progress
  $: progress = ((questionIndex + 1) / totalQuestions) * 100;
</script>

<div class="max-w-6xl mx-auto">
  <!-- Question Progress -->
  <div class="bg-white rounded-lg shadow-md p-4 mb-6">
    <div class="flex justify-between items-center mb-2">
      <span class="text-sm font-medium text-gray-700">
        Question {questionIndex + 1} of {totalQuestions} - Results
      </span>
      <span class="text-sm text-gray-600">{players.length} players</span>
    </div>
    <div class="w-full bg-gray-200 rounded-full h-2">
      <div 
        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
        style="width: {progress}%"
      ></div>
    </div>
  </div>

  <div class="grid lg:grid-cols-2 gap-6">
    <!-- Question Results -->
    <div>
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">Question Results</h2>
        
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-2">{question.name}</h3>
        </div>

        <!-- Answer Choices with Results -->
        <div class="space-y-3">
          {#each question.choices as choice (choice.id)}
            <div class="p-4 rounded-lg border-2 {
              choice.correct 
                ? 'border-green-500 bg-green-50' 
                : 'border-gray-200 bg-gray-50'
            }">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <span class="text-lg font-medium">{choice.name}</span>
                  {#if choice.correct}
                    <svg class="w-5 h-5 text-green-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span class="text-green-600 font-semibold ml-1">Correct</span>
                  {/if}
                </div>
                <!-- You could add answer statistics here if available -->
              </div>
            </div>
          {/each}
        </div>
      </div>

      <!-- Host Controls -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Next Action</h3>
        <div class="flex flex-wrap gap-3">
          {#if questionIndex < totalQuestions - 1}
            <button 
              class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-semibold"
              onclick={onNextQuestion}
            >
              Next Question ({questionIndex + 2} of {totalQuestions})
            </button>
          {:else}
            <button 
              class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors font-semibold"
              onclick={onEndGame}
            >
              Finish Game
            </button>
          {/if}
          
          <button 
            class="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
            onclick={onEndGame}
          >
            End Game Early
          </button>
        </div>
      </div>
    </div>

    <!-- Current Leaderboard -->
    <div>
      <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Leaderboard</h3>
        
        {#if sortedPlayers.length === 0}
          <p class="text-gray-600 text-center py-4">No players</p>
        {:else}
          <div class="space-y-3">
            {#each sortedPlayers as player, index (player.id)}
              <div class="leaderboard-entry {
                index === 0 ? 'bg-yellow-50 border-yellow-200' : 
                index === 1 ? 'bg-gray-50 border-gray-200' : 
                index === 2 ? 'bg-orange-50 border-orange-200' : 
                'bg-white border-gray-100'
              } border rounded-lg">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold mr-3 {
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      index === 2 ? 'bg-orange-400' : 
                      'bg-blue-500'
                    }">
                      {index + 1}
                    </div>
                    <div>
                      <p class="font-semibold text-gray-900">{player.name}</p>
                      <p class="text-sm text-gray-600">
                        {player.is_connected ? 'Connected' : 'Disconnected'}
                      </p>
                    </div>
                  </div>
                  <div class="text-right">
                    <p class="text-xl font-bold text-blue-600">{player.points}</p>
                    <p class="text-sm text-gray-500">points</p>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        {/if}
      </div>

      <!-- Game Statistics -->
      <div class="bg-blue-50 rounded-lg p-4 mt-6">
        <h4 class="font-semibold text-blue-900 mb-2">Game Statistics</h4>
        <div class="text-sm text-blue-800 space-y-1">
          <p>• Questions completed: {questionIndex + 1} of {totalQuestions}</p>
          <p>• Active players: {players.filter(p => p.is_connected).length}</p>
          <p>• Total players: {players.length}</p>
          {#if sortedPlayers.length > 0}
            <p>• Highest score: {sortedPlayers[0].points} points</p>
          {/if}
        </div>
      </div>
    </div>
  </div>
</div>
