from django.contrib import admin
from .models import Game, Player, PlayerAnswer

# Register your models here.
class PlayerInline(admin.TabularInline):
    model = Player
    extra = 0  # No empty forms for new players
    readonly_fields = ('id', 'name', 'points', 'is_connected')
    can_delete = False

class PlayerAnswerInline(admin.TabularInline):
    model = PlayerAnswer
    extra = 0  # No empty forms for new answers
    readonly_fields = ('player', 'question', 'choice', 'time_taken', 'points', 'created_at')
    can_delete = False

class PlayerAdmin(admin.ModelAdmin):
    list_display = ('name', 'game', 'points', 'is_connected')
    list_filter = ('game', 'is_connected')
    search_fields = ('name',)
    readonly_fields = ('id',)

class PlayerAnswerAdmin(admin.ModelAdmin):
    list_display = ('player', 'question', 'choice', 'time_taken', 'points', 'created_at')
    list_filter = ('player__game', 'player')
    search_fields = ('player__name', 'question__name')
    readonly_fields = ('created_at',)

class GameAdmin(admin.ModelAdmin):
    list_display = ('code', 'quiz', 'state', 'is_active', 'created_at')
    list_filter = ('quiz', 'state', 'is_active')
    search_fields = ('code', 'quiz__name')
    readonly_fields = ('id', 'created_at')
    inlines = [PlayerInline]

# Register the models with their admin classes
admin.site.register(Game, GameAdmin)
admin.site.register(Player, PlayerAdmin)
admin.site.register(PlayerAnswer, PlayerAnswerAdmin)
