from django.db import models
import uuid
# Import models from the quizzes app
from quizzes.models import Quiz, QuizQuestion, QuizChoice

# Create your models here.
class Game(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quiz = models.ForeignKey(Quiz, on_delete=models.CASCADE)
    code = models.CharField(max_length=6, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    STATE_CHOICES = [
        ('lobby', 'Lobby'),
        ('play', 'Play'),
        ('intermission', 'Intermission'),
        ('reveal', 'Reveal'),
        ('end', 'End'),
    ]
    state = models.CharField(max_length=12, choices=STATE_CHOICES, default='lobby')
    current_question = models.ForeignKey(QuizQuestion, null=True, blank=True, on_delete=models.SET_NULL)
    
    def __str__(self):
        return f"Game {self.code} - {self.quiz.name}"

class Player(models.Model):
    id = models.UUI<PERSON>ield(primary_key=True, default=uuid.uuid4, editable=False)
    game = models.ForeignKey(Game, related_name='players', on_delete=models.CASCADE)
    name = models.CharField(max_length=50)
    points = models.IntegerField(default=0)
    is_connected = models.BooleanField(default=True)
    
    def __str__(self):
        return self.name

class PlayerAnswer(models.Model):
    player = models.ForeignKey(Player, related_name='answers', on_delete=models.CASCADE)
    question = models.ForeignKey(QuizQuestion, on_delete=models.CASCADE)
    choice = models.ForeignKey(QuizChoice, on_delete=models.CASCADE)
    time_taken = models.FloatField()  # Time taken to answer in seconds
    points = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('player', 'question')
    
    def __str__(self):
        return f"{self.player.name}'s answer to {self.question.name}"
