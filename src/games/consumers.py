"""
WebSocket consumers for the games app.
"""

import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Game, Player, PlayerAnswer
from quizzes.models import Quiz, QuizQuestion, QuizChoice

class GameConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for game-related real-time communication.
    Handles player connections, game state changes, and real-time updates.
    """
    
    async def connect(self):
        """
        Called when the WebSocket is handshaking as part of the connection process.
        """
        await self.accept()
        self.game_id = None
        self.player_id = None
        self.game_group_name = None
    
    async def disconnect(self, close_code):
        """
        Called when the WebSocket closes for any reason.
        """
        if self.game_group_name:
            # Leave the game group
            await self.channel_layer.group_discard(
                self.game_group_name,
                self.channel_name
            )
        
        if self.player_id:
            # Mark the player as disconnected in the database
            await self.set_player_disconnected()
            
            # Notify other players about the disconnection
            if self.game_group_name:
                await self.channel_layer.group_send(
                    self.game_group_name,
                    {
                        'type': 'player_disconnect',
                        'player_id': self.player_id
                    }
                )
    
    async def receive(self, text_data):
        """
        Called when we receive a text frame from the client.
        """
        try:
            data = json.loads(text_data)
            packet_id = data.get('id')
            
            # Handle different packet types based on the ID
            if packet_id == 0:  # Connect
                await self.handle_connect(data)
            elif packet_id == 1:  # HostGame
                await self.handle_host_game(data)
            elif packet_id == 2:  # QuestionShow
                await self.handle_question_show(data)
            elif packet_id == 3:  # ChangeGameState
                await self.handle_change_game_state(data)
            elif packet_id == 4:  # PlayerJoin
                await self.handle_player_join(data)
            elif packet_id == 5:  # StartGame
                await self.handle_start_game(data)
            elif packet_id == 7:  # Answer
                await self.handle_answer(data)
            else:
                await self.send(json.dumps({
                    'error': f'Unknown packet type: {packet_id}'
                }))
        except json.JSONDecodeError:
            await self.send(json.dumps({
                'error': 'Invalid JSON'
            }))
        except Exception as e:
            await self.send(json.dumps({
                'error': f'Error processing message: {str(e)}'
            }))
    
    # Handler methods for different packet types
    
    async def handle_connect(self, data):
        """
        Handle player connection to a game.
        """
        code = data.get('code')
        name = data.get('name')
        
        if not code or not name:
            await self.send(json.dumps({
                'error': 'Game code and player name are required'
            }))
            return
        
        # Get the game by code
        game = await self.get_game_by_code(code)
        if not game:
            await self.send(json.dumps({
                'error': 'Game not found'
            }))
            return
        
        # Create a new player
        player = await self.create_player(game, name)
        self.player_id = str(player.id)
        self.game_id = str(game.id)
        self.game_group_name = f'game_{self.game_id}'
        
        # Join the game group
        await self.channel_layer.group_add(
            self.game_group_name,
            self.channel_name
        )
        
        # Send confirmation to the player
        await self.send(json.dumps({
            'id': 4,  # PlayerJoin packet
            'player': {
                'id': self.player_id,
                'name': name
            }
        }))
        
        # Notify other players
        await self.channel_layer.group_send(
            self.game_group_name,
            {
                'type': 'player_join',
                'player': {
                    'id': self.player_id,
                    'name': name
                }
            }
        )
    
    async def handle_host_game(self, data):
        """
        Handle game hosting request.
        """
        quiz_id = data.get('quizId')
        
        if not quiz_id:
            await self.send(json.dumps({
                'error': 'Quiz ID is required'
            }))
            return
        
        # Create a new game
        game = await self.create_game(quiz_id)
        if not game:
            await self.send(json.dumps({
                'error': 'Failed to create game'
            }))
            return
        
        self.game_id = str(game.id)
        self.game_group_name = f'game_{self.game_id}'
        
        # Join the game group
        await self.channel_layer.group_add(
            self.game_group_name,
            self.channel_name
        )
        
        # Send confirmation to the host
        await self.send(json.dumps({
            'id': 1,  # HostGame packet
            'gameId': self.game_id,
            'code': game.code
        }))
    
    async def handle_question_show(self, data):
        """
        Handle showing a question to players.
        """
        if not self.game_id:
            await self.send(json.dumps({
                'error': 'Not connected to a game'
            }))
            return
        
        question_id = data.get('questionId')
        if not question_id:
            await self.send(json.dumps({
                'error': 'Question ID is required'
            }))
            return
        
        # Get the question
        question = await self.get_question(question_id)
        if not question:
            await self.send(json.dumps({
                'error': 'Question not found'
            }))
            return
        
        # Update the game's current question
        await self.update_game_question(self.game_id, question_id)
        
        # Send the question to all players
        await self.channel_layer.group_send(
            self.game_group_name,
            {
                'type': 'question_show',
                'question': await self.serialize_question(question)
            }
        )
    
    async def handle_change_game_state(self, data):
        """
        Handle game state changes.
        """
        if not self.game_id:
            await self.send(json.dumps({
                'error': 'Not connected to a game'
            }))
            return
        
        state = data.get('state')
        if state is None:
            await self.send(json.dumps({
                'error': 'Game state is required'
            }))
            return
        
        # Map numeric state to string state
        state_map = {
            0: 'lobby',
            1: 'play',
            2: 'intermission',
            3: 'reveal',
            4: 'end'
        }
        
        db_state = state_map.get(state)
        if db_state is None:
            await self.send(json.dumps({
                'error': f'Invalid game state: {state}'
            }))
            return
        
        # Update the game state
        await self.update_game_state(self.game_id, db_state)
        
        # Notify all players about the state change
        await self.channel_layer.group_send(
            self.game_group_name,
            {
                'type': 'change_game_state',
                'state': state
            }
        )
    
    async def handle_start_game(self, data):
        """
        Handle game start request.
        """
        if not self.game_id:
            await self.send(json.dumps({
                'error': 'Not connected to a game'
            }))
            return
        
        # Update the game state to 'play'
        await self.update_game_state(self.game_id, 'play')
        
        # Get the first question
        first_question = await self.get_first_question(self.game_id)
        if not first_question:
            await self.send(json.dumps({
                'error': 'No questions available'
            }))
            return
        
        # Update the game's current question
        await self.update_game_question(self.game_id, str(first_question.id))
        
        # Notify all players about the state change
        await self.channel_layer.group_send(
            self.game_group_name,
            {
                'type': 'change_game_state',
                'state': 1  # Play state
            }
        )
        
        # Send the first question to all players
        await self.channel_layer.group_send(
            self.game_group_name,
            {
                'type': 'question_show',
                'question': await self.serialize_question(first_question)
            }
        )
    
    async def handle_answer(self, data):
        """
        Handle player answer submission.
        """
        if not self.player_id or not self.game_id:
            await self.send(json.dumps({
                'error': 'Not connected to a game'
            }))
            return
        
        question_id = data.get('questionId')
        choice_id = data.get('choiceId')
        time_taken = data.get('timeTaken')
        
        if not all([question_id, choice_id, time_taken is not None]):
            await self.send(json.dumps({
                'error': 'Question ID, choice ID, and time taken are required'
            }))
            return
        
        # Record the player's answer
        result = await self.record_answer(
            self.player_id, 
            question_id, 
            choice_id, 
            float(time_taken)
        )
        
        if not result:
            await self.send(json.dumps({
                'error': 'Failed to record answer'
            }))
            return
        
        # Send confirmation to the player
        await self.send(json.dumps({
            'id': 7,  # Answer packet
            'success': True,
            'points': result['points'],
            'correct': result['correct']
        }))
    
    # Event handlers for channel layer messages
    
    async def player_join(self, event):
        """
        Send player join notification to the client.
        """
        await self.send(json.dumps({
            'id': 4,  # PlayerJoin packet
            'player': event['player']
        }))
    
    async def player_disconnect(self, event):
        """
        Send player disconnect notification to the client.
        """
        await self.send(json.dumps({
            'id': 10,  # PlayerDisconnect packet
            'playerId': event['player_id']
        }))
    
    async def question_show(self, event):
        """
        Send question to the client.
        """
        await self.send(json.dumps({
            'id': 2,  # QuestionShow packet
            'question': event['question']
        }))
    
    async def change_game_state(self, event):
        """
        Send game state change to the client.
        """
        await self.send(json.dumps({
            'id': 3,  # ChangeGameState packet
            'state': event['state']
        }))
    
    # Database access methods
    
    @database_sync_to_async
    def get_game_by_code(self, code):
        """
        Get a game by its code.
        """
        try:
            return Game.objects.get(code=code.upper(), is_active=True)
        except Game.DoesNotExist:
            return None
    
    @database_sync_to_async
    def create_player(self, game, name):
        """
        Create a new player for a game.
        """
        return Player.objects.create(game=game, name=name)
    
    @database_sync_to_async
    def set_player_disconnected(self):
        """
        Mark a player as disconnected.
        """
        try:
            player = Player.objects.get(id=self.player_id)
            player.is_connected = False
            player.save()
            return True
        except Player.DoesNotExist:
            return False
    
    @database_sync_to_async
    def create_game(self, quiz_id):
        """
        Create a new game for a quiz.
        """
        try:
            quiz = Quiz.objects.get(id=quiz_id)
            
            # Generate a unique game code
            import random
            import string
            code = ''.join(random.choices(string.ascii_uppercase, k=6))
            while Game.objects.filter(code=code).exists():
                code = ''.join(random.choices(string.ascii_uppercase, k=6))
            
            return Game.objects.create(quiz=quiz, code=code)
        except Quiz.DoesNotExist:
            return None
    
    @database_sync_to_async
    def get_question(self, question_id):
        """
        Get a question by its ID.
        """
        try:
            return QuizQuestion.objects.get(id=question_id)
        except QuizQuestion.DoesNotExist:
            return None
    
    @database_sync_to_async
    def update_game_question(self, game_id, question_id):
        """
        Update a game's current question.
        """
        try:
            game = Game.objects.get(id=game_id)
            question = QuizQuestion.objects.get(id=question_id)
            game.current_question = question
            game.save()
            return True
        except (Game.DoesNotExist, QuizQuestion.DoesNotExist):
            return False
    
    @database_sync_to_async
    def update_game_state(self, game_id, state):
        """
        Update a game's state.
        """
        try:
            game = Game.objects.get(id=game_id)
            game.state = state
            game.save()
            return True
        except Game.DoesNotExist:
            return False
    
    @database_sync_to_async
    def get_first_question(self, game_id):
        """
        Get the first question for a game.
        """
        try:
            game = Game.objects.get(id=game_id)
            return game.quiz.questions.order_by('order').first()
        except Game.DoesNotExist:
            return None
    
    @database_sync_to_async
    def record_answer(self, player_id, question_id, choice_id, time_taken):
        """
        Record a player's answer to a question.
        """
        try:
            player = Player.objects.get(id=player_id)
            question = QuizQuestion.objects.get(id=question_id)
            choice = QuizChoice.objects.get(id=choice_id)
            
            # Calculate points
            points = 0
            if choice.correct:
                # Award points based on how quickly they answered
                max_points = 1000
                time_factor = max(0, 1 - (time_taken / question.time))
                points = int(max_points * time_factor)
            
            # Create or update the player's answer
            answer, created = PlayerAnswer.objects.update_or_create(
                player=player,
                question=question,
                defaults={
                    'choice': choice,
                    'time_taken': time_taken,
                    'points': points
                }
            )
            
            # Update player's total points
            from django.db.models import Sum
            player.points = PlayerAnswer.objects.filter(player=player).aggregate(Sum('points'))['points__sum'] or 0
            player.save()
            
            return {
                'points': points,
                'correct': choice.correct,
                'total_points': player.points
            }
        except (Player.DoesNotExist, QuizQuestion.DoesNotExist, QuizChoice.DoesNotExist):
            return None
    
    @database_sync_to_async
    def serialize_question(self, question):
        """
        Serialize a question for sending over WebSocket.
        """
        choices = []
        for choice in question.choices.all():
            choices.append({
                'id': str(choice.id),
                'name': choice.name,
                # Don't send the 'correct' field to clients
            })
        
        return {
            'id': str(question.id),
            'name': question.name,
            'time': question.time,
            'choices': choices
        }