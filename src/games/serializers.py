from rest_framework import serializers
from .models import Game, Player, PlayerAnswer
from quizzes.serializers import QuizSerializer, QuizQuestionSerializer, QuizChoiceSerializer
from quizzes.models import Quiz, QuizQuestion, QuizChoice

class PlayerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Player
        fields = ['id', 'name', 'points', 'is_connected']
        read_only_fields = ['id', 'points', 'is_connected']

class PlayerAnswerSerializer(serializers.ModelSerializer):
    question_details = QuizQuestionSerializer(source='question', read_only=True)
    choice_details = QuizChoiceSerializer(source='choice', read_only=True)
    
    class Meta:
        model = PlayerAnswer
        fields = ['player', 'question', 'choice', 'time_taken', 'points', 'created_at', 'question_details', 'choice_details']
        read_only_fields = ['created_at', 'points']

class GameSerializer(serializers.ModelSerializer):
    quiz_details = QuizSerializer(source='quiz', read_only=True)
    players = PlayerSerializer(many=True, read_only=True)
    current_question_details = QuizQuestionSerializer(source='current_question', read_only=True)
    
    class Meta:
        model = Game
        fields = ['id', 'quiz', 'code', 'state', 'is_active', 'created_at', 'current_question', 
                 'quiz_details', 'players', 'current_question_details']
        read_only_fields = ['id', 'code', 'created_at']
    
    def create(self, validated_data):
        # Generate a unique game code (6 uppercase letters)
        import random
        import string
        code = ''.join(random.choices(string.ascii_uppercase, k=6))
        while Game.objects.filter(code=code).exists():
            code = ''.join(random.choices(string.ascii_uppercase, k=6))
        
        validated_data['code'] = code
        return super().create(validated_data)

class GameJoinSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=6)
    name = serializers.CharField(max_length=50)
    
    def validate_code(self, value):
        try:
            game = Game.objects.get(code=value.upper(), is_active=True)
            if game.state != 'lobby':
                raise serializers.ValidationError("Game has already started")
            return value.upper()
        except Game.DoesNotExist:
            raise serializers.ValidationError("Invalid game code")

class LeaderboardEntrySerializer(serializers.Serializer):
    name = serializers.CharField()
    points = serializers.IntegerField()

class LeaderboardSerializer(serializers.Serializer):
    entries = LeaderboardEntrySerializer(many=True)