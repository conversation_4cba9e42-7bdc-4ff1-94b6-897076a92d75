from django.shortcuts import render, get_object_or_404
from django.db import models
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Game, Player, PlayerAnswer
from .serializers import (
    GameSerializer, PlayerSerializer, PlayerAnswerSerializer,
    GameJoinSerializer, LeaderboardSerializer, LeaderboardEntrySerializer
)
# Import models from the quizzes app
from quizzes.models import Quiz, QuizQuestion, QuizChoice
import random
import string

# Create your views here.
class GameViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows games to be viewed or edited.
    """
    queryset = Game.objects.all().order_by('-created_at')
    serializer_class = GameSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    @action(detail=False, methods=['post'])
    def create_game(self, request):
        """
        Create a new game with a unique code.
        """
        quiz_id = request.data.get('quiz')
        if not quiz_id:
            return Response({'error': 'Quiz ID is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            quiz = Quiz.objects.get(pk=quiz_id)
        except Quiz.DoesNotExist:
            return Response({'error': 'Quiz not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Generate a unique game code (6 uppercase letters)
        code = ''.join(random.choices(string.ascii_uppercase, k=6))
        while Game.objects.filter(code=code).exists():
            code = ''.join(random.choices(string.ascii_uppercase, k=6))
        
        game = Game.objects.create(quiz=quiz, code=code)
        serializer = self.get_serializer(game)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=False, methods=['post'])
    def join_game(self, request):
        """
        Join an existing game using a game code and player name.
        """
        serializer = GameJoinSerializer(data=request.data)
        if serializer.is_valid():
            code = serializer.validated_data['code']
            name = serializer.validated_data['name']
            
            try:
                game = Game.objects.get(code=code, is_active=True)
                if game.state != 'lobby':
                    return Response({'error': 'Game has already started'}, status=status.HTTP_400_BAD_REQUEST)
                
                player = Player.objects.create(game=game, name=name)
                return Response({
                    'player_id': str(player.id),
                    'game_id': str(game.id),
                    'game_code': game.code
                }, status=status.HTTP_201_CREATED)
            except Game.DoesNotExist:
                return Response({'error': 'Invalid game code'}, status=status.HTTP_404_NOT_FOUND)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def start_game(self, request, pk=None):
        """
        Start a game by changing its state from 'lobby' to 'play'.
        """
        game = self.get_object()
        if game.state != 'lobby':
            return Response({'error': 'Game has already started'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Set the first question as the current question
        first_question = game.quiz.questions.order_by('order').first()
        if not first_question:
            return Response({'error': 'Quiz has no questions'}, status=status.HTTP_400_BAD_REQUEST)
        
        game.current_question = first_question
        game.state = 'play'
        game.save()
        
        serializer = self.get_serializer(game)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def players(self, request, pk=None):
        """
        Return a list of all players in a specific game.
        """
        game = self.get_object()
        players = game.players.all()
        serializer = PlayerSerializer(players, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def leaderboard(self, request, pk=None):
        """
        Return the leaderboard for a specific game.
        """
        game = self.get_object()
        players = game.players.filter(is_connected=True).order_by('-points')
        
        entries = [{'name': player.name, 'points': player.points} for player in players]
        serializer = LeaderboardSerializer(data={'entries': entries})
        serializer.is_valid()  # This should always be valid given our construction
        
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def next_question(self, request, pk=None):
        """
        Advance to the next question in the quiz.
        """
        game = self.get_object()
        if game.state not in ['play', 'reveal']:
            return Response({'error': 'Game is not in play or reveal state'}, status=status.HTTP_400_BAD_REQUEST)
        
        current_question = game.current_question
        if not current_question:
            return Response({'error': 'No current question'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the next question based on order
        next_question = game.quiz.questions.filter(order__gt=current_question.order).order_by('order').first()
        
        if next_question:
            game.current_question = next_question
            game.state = 'play'
            game.save()
            serializer = self.get_serializer(game)
            return Response(serializer.data)
        else:
            # No more questions, end the game
            game.state = 'end'
            game.save()
            serializer = self.get_serializer(game)
            return Response(serializer.data)

class PlayerViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows players to be viewed or edited.
    """
    queryset = Player.objects.all()
    serializer_class = PlayerSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Optionally restricts the returned players to those in a specific game,
        by filtering against a `game` query parameter in the URL.
        """
        queryset = Player.objects.all()
        game_id = self.request.query_params.get('game', None)
        if game_id is not None:
            queryset = queryset.filter(game__id=game_id)
        return queryset
    
    @action(detail=True, methods=['post'])
    def submit_answer(self, request, pk=None):
        """
        Submit an answer for a player.
        """
        player = self.get_object()
        game = player.game
        
        if game.state != 'play':
            return Response({'error': 'Game is not in play state'}, status=status.HTTP_400_BAD_REQUEST)
        
        question_id = request.data.get('question')
        choice_id = request.data.get('choice')
        time_taken = request.data.get('time_taken')
        
        if not all([question_id, choice_id, time_taken]):
            return Response({'error': 'Question ID, choice ID, and time taken are required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            question = QuizQuestion.objects.get(pk=question_id)
            choice = question.choices.get(pk=choice_id)
        except (QuizQuestion.DoesNotExist, QuizChoice.DoesNotExist):
            return Response({'error': 'Question or choice not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Calculate points based on correctness and time taken
        points = 0
        if choice.correct:
            # Award points based on how quickly they answered
            max_points = 1000
            time_factor = max(0, 1 - (float(time_taken) / question.time))
            points = int(max_points * time_factor)
        
        # Create or update the player's answer
        player_answer, created = PlayerAnswer.objects.update_or_create(
            player=player,
            question=question,
            defaults={
                'choice': choice,
                'time_taken': time_taken,
                'points': points
            }
        )
        
        # Update player's total points
        player.points = PlayerAnswer.objects.filter(player=player).aggregate(models.Sum('points'))['points__sum'] or 0
        player.save()
        
        return Response({
            'points': points,
            'correct': choice.correct,
            'total_points': player.points
        })

class PlayerAnswerViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows player answers to be viewed.
    """
    queryset = PlayerAnswer.objects.all()
    serializer_class = PlayerAnswerSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Optionally restricts the returned answers to those by a specific player,
        by filtering against a `player` query parameter in the URL.
        """
        queryset = PlayerAnswer.objects.all()
        player_id = self.request.query_params.get('player', None)
        if player_id is not None:
            queryset = queryset.filter(player__id=player_id)
        return queryset
