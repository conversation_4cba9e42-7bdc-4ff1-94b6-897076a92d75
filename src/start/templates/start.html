{% extends 'base.html' %}
{% load static %}
{% block content %}
{% load markdown_extras %}
{% include 'components/top-navbar.html' %}
{% include 'components/loading_overlay.html' %}

<div class="min-h-screen bg-base-200">
    <main class="mx-4 md:mx-16 mt-4 md:mt-8 mb-4 md:mb-16 space-y-4 md:space-y-6">

    <!-- Heading Section -->
    <section id="intro" class="space-y-2">
        <h1 class="text-lg md:text-xl font-medium font-roboto mb-2">Start Your Content Project</h1>
        <p class="font-poppins text-xs md:text-sm">Begin by choosing a niche you're passionate about.</p>
        <p class="font-poppins text-xs md:text-sm">
            We'll guide you step-by-step to define a real problem, validate it with data, and craft a compelling title and marketing angle.
        </p>
        <p class="font-poppins text-xs md:text-sm">
        Once your idea is solid, you’ll move on to creating powerful content for your product.
        </p>
    </section>

    <!-- Step Progress Bar -->
    <section id="workflow-steps" class="font-poppins font-medium text-xs md:text-sm py-6">
        <ul class="steps steps-horizontal w-full overflow-x-auto sm:overflow-visible">
        <li class="step text-xs md:text-sm  step-primary text-primary ">Niche</li>
        <li class="step text-xs md:text-sm  text-gray-300">Problems</li>
        <li class="step text-xs md:text-sm text-gray-300">Validation</li>
        <li class="step text-xs md:text-sm text-gray-300">Research</li>
        <li class="step text-xs md:text-sm  text-gray-300">Titles</li>
        </ul>
    </section>

    <!-- Niche Input Form -->
    <section id="niche-form" class="bg-base-100 border border-base-300 rounded-sm p-6 shadow-sm space-y-4 md:space-y-6">
        <p class="font-poppins text-xs md:text-sm">
        Enter your niche or area of interest, and we'll help you identify specific problems and opportunities for valuable content that can be used across multiple platforms.
        </p>

        <form method="post" class="space-y-4 md:space-y-6" action="{% url 'ideation:start' %}">
        {% csrf_token %}
        <div class="form-control w-full">
        <label for="niche" class="w-full">
        <p id="niche" class="font-poppins text-xs md:text-sm whitespace-normal mb-2">
        What niche or area of interest would you like to create content for?
        </p>
        </label>
        <input type="text"
        name="{{ form.niche.name }}"
        id="{{ form.niche.id_for_label }}"
        placeholder="e.g., Gut Health for Women"
        class="input input-common w-full "
        value="{{ form.niche.value|default:'' }}">
        <label for="niche-examples" class="w-full">
        <p id="niche-examples" class="font-poppins text-xs md:text-sm whitespace-normal mt-2"  >
        <span class="font-medium">Examples:</span> Gut Health for Women, Productivity for Entrepreneurs, Mindfulness for Parents
        </p>
        </label>
        {% if form.niche.errors %}
        <div class="text-error mt-1">
        <p class="font-poppins text-xs md:text-sm">{{ form.niche.errors }}</p>
        </div>
        {% endif %}
        </div>

        <div class="flex w-full">
        <button
        type="submit"
        class="text-xs md:text-sm btn btn-primary w-full px-6 py-3">
        Generate Problem Statements <i class="fa-regular fa-arrow-right ml-2"></i>
        </button>
        </div>
        </form>
    </section>
    {#Existing Projects#}
    {% if existing_entries %}
    <div class="text-xs md:text-sm bg-base-100 border border-base-300 rounded-sm p-6 shadow-sm space-y-4 md:space-y-6">

    <h2 class="font-roboto text-base md:text-lg font-medium">Your Existing Ideation Projects</h2>
    <p class="font-poppins text-xs md:text-sm">Continue working on one of your existing projects:</p>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
    {% for entry in existing_entries %}
<a href="{% url 'ideation:problem_selection' entry.id %}"
class="bg-base-100 shadow-sm border border-base-300 hover:bg-base-200 space-y-4 md:space-y-6 transition-colors">
  <div class="p-4 flex flex-col h-full">
    <h3 class="font-roboto text-sm md:text-base font-medium">{{ entry.niche }}</h3>
    <p class="font-poppins text-accent-content text-xs md:text-sm mt-2">Created: {{ entry.created_at|date:"M d, Y"}}</p>

    <!-- Bottom content sticks to the bottom -->
    <div class="flex justify-between items-center mt-4">
      <!-- Delete button -->
      <button class="btn btn-sm btn-ghost text-error"
      onclick="event.preventDefault(); document.getElementById('delete-modal-{{ entry.id }}').showModal()">
        <i class="fa-regular fa-trash-can"></i>
      </button>

      <div>
        <span class="text-primary btn btn-sm btn-secondary-content">Continue</span>
      </div>
    </div>
  </div>
</a>


    <!-- Delete confirmation modal with enhanced styling -->
    <dialog id="delete-modal-{{ entry.id }}" class="modal">
    <div class="modal-box">


    <div class="flex-1 space-y-4 md:space-y-6">
        <div class="flex flex-row items-center justify-start gap-4">
            <i class="text-error fa-regular fa-triangle-exclamation fa-2x"></i>
            <h3 class="font-roboto text-sm md:text-base font-medium">Delete Project</h3>
        </div>
    <div class="py-2 text-base-content">
    <p class="font-poppins text-xs md:text-sm">
    Are you sure you want to delete "<span class="font-bold">{{ entry.niche }}</span>"?
    </p>
    <div class="bg-error/10 border-l-4 border-error p-4 rounded-r my-3">
    <p class="font-poppins text-xs md:text-sm">
        <span class="font-medium">WARNING:</span> This will permanently remove all associated:
    </p>
    <ul class="list-disc font-poppins ml-5 mt-2 text-xs md:text-sm space-y-1">
    <li>Problem statements</li>
    <li>Research documents</li>
    <li>Content outlines</li>
    <li>Completed PDFs</li>
    </ul>
    <p class="font-poppins text-xs md:text-sm">This action cannot be undone.</p>
    </div>
    </div>

    </div>
    <div class="modal-action mt-6 flex justify-between gap-2">
    <button class="btn btn-sm ghost"
    onclick="document.getElementById('delete-modal-{{ entry.id }}').close()">
    Cancel
    </button>
    <form method="POST" action="{% url 'ideation:delete_entry' entry.id %}">
    {% csrf_token %}
    <button type="submit" class="btn  btn-sm btn-error">
    <i class="fa-regular fa-trash-can mr-2"></i>Delete Project
    </button>
    </form>
    </div>
    </div>
    <form method="dialog" class="modal-backdrop">
    <button>close</button>
    </form>
    </dialog>
    {% endfor %}
    </div>


    {% endif %}
    <!-- Tips Section -->
<section id="ideation-tips" class="space-y-4 md:space-y-6 py-6">
<h2 class="font-roboto text-base md:text-lg font-medium">Tips for Effective Ideation</h2>
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
<div class="bg-base-100 border border-base-300 rounded-sm p-6 shadow-sm">
<h3 class="font-roboto text-sm md:text-base font-medium">Be specific</h3>
<p class="font-poppins text-xs md:text-sm">
Instead of "Health," try "Gut Health for Women Over 40"
</p>
</div>
<div class="bg-base-100 border border-base-300 rounded-sm p-6 shadow-sm">
<h3 class="font-roboto text-sm md:text-base font-medium">Consider your expertise</h3>
<p class="font-poppins text-xs md:text-sm">
Choose niches where you have knowledge or experience
</p>
</div>
<div class="bg-base-100 border border-base-300 rounded-sm p-6 shadow-sm">
<h3 class="font-roboto text-sm md:text-base font-medium">Think about audience needs</h3>
<p class="font-poppins text-xs md:text-sm">
What specific problems do people in this niche face?
</p>
</div>
<div class="bg-base-100 border border-base-300 rounded-sm p-6 shadow-sm">
<h3 class="font-roboto text-sm md:text-base font-medium">Consider market demand</h3>
<p class="font-poppins text-xs md:text-sm">
Look for niches with active online communities and discussions
</p>
</div>
</div>
</section>


        </div>
    </main>
</div>

{% endblock %}
