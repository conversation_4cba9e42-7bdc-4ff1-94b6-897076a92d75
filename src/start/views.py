#start/views.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from logger.logging_util import logger

# Create your views here.
@login_required
def start(request):
    """Start the ideation process."""
    # Store the current view in the session
    request.session["current_view"] = "ideation:start"
    # Debug message to verify authentication
    logger.debug(
        f"User authenticated: {request.user.is_authenticated}, Username: {request.user.username}"
    )



    return render(request, "start.html")

