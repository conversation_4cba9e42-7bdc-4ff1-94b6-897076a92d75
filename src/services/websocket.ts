import type { 
    Packet, 
    ConnectPacket, 
    HostGamePacket, 
    QuestionShowPacket, 
    ChangeGameStatePacket,
    PlayerJoinPacket,
    StartGamePacket,
    AnswerPacket,
    PacketTypes
} from '../types/quiz';

export class WebSocketService {
    private webSocket: WebSocket | null = null;
    private onPacketCallback?: (packet: Packet) => void;
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000; // Start with 1 second
    
    // Reactive state using Svelte 5 runes
    connected = $state(false);
    connecting = $state(false);
    error = $state<string | null>(null);

    connect(url: string = "ws://localhost:8000/ws/"): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
                resolve();
                return;
            }

            this.connecting = true;
            this.error = null;

            try {
                this.webSocket = new WebSocket(url);

                this.webSocket.onopen = () => {
                    console.log("WebSocket connection opened");
                    this.connected = true;
                    this.connecting = false;
                    this.reconnectAttempts = 0;
                    this.reconnectDelay = 1000;
                    resolve();
                };

                this.webSocket.onmessage = (event: MessageEvent) => {
                    try {
                        const packet = JSON.parse(event.data);
                        console.log("Received packet:", packet);
                        
                        if (this.onPacketCallback) {
                            this.onPacketCallback(packet);
                        }
                    } catch (err) {
                        console.error("Failed to parse WebSocket message:", err);
                        this.error = "Failed to parse message from server";
                    }
                };

                this.webSocket.onclose = (event) => {
                    console.log("WebSocket connection closed:", event.code, event.reason);
                    this.connected = false;
                    this.connecting = false;
                    
                    // Attempt to reconnect if it wasn't a clean close
                    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                        this.attemptReconnect(url);
                    }
                };

                this.webSocket.onerror = (error) => {
                    console.error("WebSocket error:", error);
                    this.error = "WebSocket connection error";
                    this.connected = false;
                    this.connecting = false;
                    reject(new Error("WebSocket connection failed"));
                };

            } catch (err) {
                this.connecting = false;
                this.error = "Failed to create WebSocket connection";
                reject(err);
            }
        });
    }

    private attemptReconnect(url: string) {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        
        setTimeout(() => {
            this.connect(url).catch(() => {
                // If reconnection fails, double the delay for next attempt
                this.reconnectDelay *= 2;
            });
        }, this.reconnectDelay);
    }

    disconnect() {
        if (this.webSocket) {
            this.webSocket.close(1000, "Client disconnecting");
            this.webSocket = null;
        }
        this.connected = false;
        this.connecting = false;
    }

    onPacket(callback: (packet: Packet) => void) {
        this.onPacketCallback = callback;
    }

    sendPacket(packet: Packet) {
        if (!this.webSocket || this.webSocket.readyState !== WebSocket.OPEN) {
            console.error("WebSocket is not connected");
            this.error = "Not connected to server";
            return;
        }

        try {
            const message = JSON.stringify(packet);
            console.log("Sending packet:", packet);
            this.webSocket.send(message);
        } catch (err) {
            console.error("Failed to send packet:", err);
            this.error = "Failed to send message to server";
        }
    }

    // Convenience methods for sending specific packet types
    sendConnect(code: string, name: string) {
        const packet: ConnectPacket = {
            id: 0, // PacketTypes.Connect
            code: code.toUpperCase(),
            name
        };
        this.sendPacket(packet);
    }

    sendHostGame(quizId: string) {
        const packet: HostGamePacket = {
            id: 1, // PacketTypes.HostGame
            quizId
        };
        this.sendPacket(packet);
    }

    sendQuestionShow(questionId: string) {
        const packet = {
            id: 2, // PacketTypes.QuestionShow
            questionId
        };
        this.sendPacket(packet);
    }

    sendChangeGameState(state: number) {
        const packet: ChangeGameStatePacket = {
            id: 3, // PacketTypes.ChangeGameState
            state: state as any // Will be mapped on server side
        };
        this.sendPacket(packet);
    }

    sendStartGame() {
        const packet: StartGamePacket = {
            id: 5 // PacketTypes.StartGame
        };
        this.sendPacket(packet);
    }

    sendAnswer(questionId: string, choiceId: string, timeTaken: number) {
        const packet: AnswerPacket = {
            id: 7, // PacketTypes.Answer
            questionId,
            choiceId,
            timeTaken
        };
        this.sendPacket(packet);
    }
}

// Create a singleton instance
export const webSocketService = new WebSocketService();
