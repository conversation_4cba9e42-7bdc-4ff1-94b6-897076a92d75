import type { Quiz, Game, Player, Leaderboard } from '../types/quiz';

// Using Svelte 5 state for loading and error states
export class ApiService {
    // Reactive state for loading and errors
    loading = $state(false);
    error = $state<string | null>(null);
    
    // Base URL for API - can be configured from environment
    apiBaseUrl = "http://localhost:8000/api";

    async getQuizById(id: string): Promise<Quiz | null> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/${id}/`);
            if (!response.ok) {
                throw new Error(`Failed to fetch quiz: ${response.statusText}`);
            }

            let json = await response.json();
            return json;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return null;
        } finally {
            this.loading = false;
        }
    }

    async getQuizzes(): Promise<Quiz[]> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/`);
            if (!response.ok) {
                throw new Error(`Failed to fetch quizzes: ${response.statusText}`);
            }

            let json = await response.json();
            return json.results || json; // Handle paginated response
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return [];
        } finally {
            this.loading = false;
        }
    }

    async saveQuiz(quiz: Quiz): Promise<Quiz | null> {
        this.loading = true;
        this.error = null;
        
        try {
            const url = quiz.id ? 
                `${this.apiBaseUrl}/quizzes/${quiz.id}/` : 
                `${this.apiBaseUrl}/quizzes/`;
            
            const method = quiz.id ? 'PUT' : 'POST';
            
            let response = await fetch(url, {
                method: method,
                body: JSON.stringify(quiz),
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to save quiz: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return null;
        } finally {
            this.loading = false;
        }
    }
    
    async createQuiz(quiz: Omit<Quiz, 'id' | 'created_at' | 'updated_at'>): Promise<Quiz | null> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/`, {
                method: "POST",
                body: JSON.stringify(quiz),
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to create quiz: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return null;
        } finally {
            this.loading = false;
        }
    }

    async deleteQuiz(id: string): Promise<boolean> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/quizzes/${id}/`, {
                method: "DELETE"
            });

            if (!response.ok) {
                throw new Error(`Failed to delete quiz: ${response.statusText}`);
            }
            
            return true;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return false;
        } finally {
            this.loading = false;
        }
    }

    // Game-related API methods
    async createGame(quizId: string): Promise<Game | null> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/games/create_game/`, {
                method: "POST",
                body: JSON.stringify({ quiz: quizId }),
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to create game: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return null;
        } finally {
            this.loading = false;
        }
    }

    async joinGame(code: string, name: string): Promise<{player_id: string, game_id: string, game_code: string} | null> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/games/join_game/`, {
                method: "POST",
                body: JSON.stringify({ code: code.toUpperCase(), name }),
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `Failed to join game: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return null;
        } finally {
            this.loading = false;
        }
    }

    async getGamePlayers(gameId: string): Promise<Player[]> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/games/${gameId}/players/`);
            if (!response.ok) {
                throw new Error(`Failed to fetch players: ${response.statusText}`);
            }

            let json = await response.json();
            return json;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return [];
        } finally {
            this.loading = false;
        }
    }

    async getGameLeaderboard(gameId: string): Promise<Leaderboard | null> {
        this.loading = true;
        this.error = null;
        
        try {
            let response = await fetch(`${this.apiBaseUrl}/games/${gameId}/leaderboard/`);
            if (!response.ok) {
                throw new Error(`Failed to fetch leaderboard: ${response.statusText}`);
            }

            let json = await response.json();
            return json;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            this.error = errorMessage;
            return null;
        } finally {
            this.loading = false;
        }
    }
}

// Create a singleton instance
export const apiService = new ApiService();
