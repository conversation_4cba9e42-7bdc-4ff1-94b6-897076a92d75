"""
TGA Web Services SOAP Client
Captures data from training.gov.au National Register
"""

import requests
from xml.etree import ElementTree as ET
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Any
import time

logger = logging.getLogger(__name__)

class TGAClient:
    def __init__(self, username: str = "sandbox", password: str = "sandbox", environment: str = 'sandbox'):
        """
        Initialize TGA Client with sandbox credentials
        
        For sandbox testing, TGA provides default credentials:
        - Username: sandbox
        - Password: sandbox
        """
        self.username = username
        self.password = password
        
        # Set base URLs based on environment
        if environment == 'production':
            self.base_url = 'https://ws.training.gov.au/Deewr.Tga.Webservices'
        elif environment == 'staging':
            self.base_url = 'https://ws.staging.training.gov.au/Deewr.Tga.Webservices'
        else:  # sandbox
            self.base_url = 'https://ws.sandbox.training.gov.au/Deewr.Tga.Webservices'
            
        self.endpoints = {
            'organisation': f'{self.base_url}/OrganisationServiceV12.svc',
            'training': f'{self.base_url}/TrainingComponentServiceV12.svc',
            'classification': f'{self.base_url}/ClassificationServiceV12.svc'
        }
        
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'text/xml; charset=utf-8',
            'SOAPAction': ''
        })

    def _create_soap_envelope(self, body_content: str, action: str) -> str:
        """Create SOAP envelope with WS-Security authentication"""
        soap_envelope = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" 
               xmlns:tga="http://training.gov.au/services/12/">
    <soap:Header>
        <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
                       xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
            <wsse:UsernameToken>
                <wsse:Username>{self.username}</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password>
            </wsse:UsernameToken>
        </wsse:Security>
    </soap:Header>
    <soap:Body>
        {body_content}
    </soap:Body>
</soap:Envelope>"""
        return soap_envelope

    def _make_soap_request(self, service: str, action: str, body_content: str) -> Optional[ET.Element]:
        """Make SOAP request to TGA service"""
        soap_envelope = self._create_soap_envelope(body_content, action)
        
        headers = self.session.headers.copy()
        headers['SOAPAction'] = f'http://training.gov.au/services/12/I{service.title()}Service/{action}'
        
        endpoint_url = f'{self.endpoints[service]}/{service.title()}'
        
        try:
            logger.info(f"Making SOAP request to {endpoint_url} for action {action}")
            response = self.session.post(
                endpoint_url, 
                data=soap_envelope.encode('utf-8'),
                headers=headers,
                timeout=30
            )
            response.raise_for_status()
            return self._parse_soap_response(response.text)
        except requests.RequestException as e:
            logger.error(f"SOAP request failed: {e}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"Response content: {e.response.text[:1000]}")
            return None

    def _parse_soap_response(self, response_xml: str) -> Optional[ET.Element]:
        """Parse SOAP response XML"""
        try:
            root = ET.fromstring(response_xml)
            # Remove namespace prefixes for easier parsing
            for elem in root.iter():
                if '}' in elem.tag:
                    elem.tag = elem.tag.split('}')[1]
            return root
        except ET.ParseError as e:
            logger.error(f"XML parsing error: {e}")
            return None

    def test_connection(self) -> bool:
        """Test connection to TGA services"""
        try:
            response = self.get_server_time()
            return response is not None
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False

    def get_server_time(self, service: str = 'training') -> Optional[str]:
        """Get server time from any service"""
        body = "<tga:GetServerTime />"
        response = self._make_soap_request(service, 'GetServerTime', body)
        
        if response:
            time_elem = response.find('.//GetServerTimeResult')
            if time_elem is not None and time_elem.text:
                return time_elem.text
        return None

    def search_units_of_competency(self, 
                                 search_term: str = "", 
                                 page_size: int = 50, 
                                 page_number: int = 1,
                                 include_superseded: bool = False) -> Optional[Dict]:
        """Search specifically for Units of Competency"""
        body = f"""
        <tga:Search>
            <tga:request>
                <tga:Filter>{search_term}</tga:Filter>
                <tga:SearchTitle>true</tga:SearchTitle>
                <tga:SearchCode>true</tga:SearchCode>
                <tga:IncludeDeleted>false</tga:IncludeDeleted>
                <tga:IncludeSuperseeded>{str(include_superseded).lower()}</tga:IncludeSuperseeded>
                <tga:PageSize>{page_size}</tga:PageSize>
                <tga:PageNumber>{page_number}</tga:PageNumber>
                <tga:TrainingComponentTypes>
                    <tga:IncludeQualification>false</tga:IncludeQualification>
                    <tga:IncludeUnit>true</tga:IncludeUnit>
                    <tga:IncludeSkillSet>false</tga:IncludeSkillSet>
                    <tga:IncludeAccreditedCourse>false</tga:IncludeAccreditedCourse>
                    <tga:IncludeAccreditedCourseModule>false</tga:IncludeAccreditedCourseModule>
                    <tga:IncludeTrainingPackage>false</tga:IncludeTrainingPackage>
                    <tga:IncludeUnitContextualisation>false</tga:IncludeUnitContextualisation>
                </tga:TrainingComponentTypes>
            </tga:request>
        </tga:Search>"""
        
        response = self._make_soap_request('training', 'Search', body)
        if response:
            return self._parse_search_response(response)
        return None

    def get_unit_details(self, unit_code: str) -> Optional[Dict]:
        """Get detailed information for a specific unit of competency"""
        body = f"""
        <tga:GetDetails>
            <tga:request>
                <tga:Code>{unit_code}</tga:Code>
                <tga:InformationRequest>
                    <tga:ShowClassifications>true</tga:ShowClassifications>
                    <tga:ShowContacts>true</tga:ShowContacts>
                    <tga:ShowCurrencyPeriods>true</tga:ShowCurrencyPeriods>
                    <tga:ShowDataManagers>true</tga:ShowDataManagers>
                    <tga:ShowReleases>true</tga:ShowReleases>
                    <tga:ShowFiles>true</tga:ShowFiles>
                    <tga:ShowMappingInformation>true</tga:ShowMappingInformation>
                    <tga:ShowUsageRecommendation>true</tga:ShowUsageRecommendation>
                    <tga:ShowIndustrySectors>true</tga:ShowIndustrySectors>
                    <tga:ShowOccupations>true</tga:ShowOccupations>
                </tga:InformationRequest>
            </tga:request>
        </tga:GetDetails>"""
        
        response = self._make_soap_request('training', 'GetDetails', body)
        if response:
            return self._parse_unit_details(response)
        return None

    def search_by_training_package(self, training_package_code: str) -> Optional[Dict]:
        """Search for units within a specific training package"""
        body = f"""
        <tga:Search>
            <tga:request>
                <tga:Filter>{training_package_code}</tga:Filter>
                <tga:SearchCode>true</tga:SearchCode>
                <tga:SearchTitle>false</tga:SearchTitle>
                <tga:IncludeDeleted>false</tga:IncludeDeleted>
                <tga:IncludeSuperseeded>false</tga:IncludeSuperseeded>
                <tga:PageSize>100</tga:PageSize>
                <tga:PageNumber>1</tga:PageNumber>
                <tga:TrainingComponentTypes>
                    <tga:IncludeQualification>false</tga:IncludeQualification>
                    <tga:IncludeUnit>true</tga:IncludeUnit>
                    <tga:IncludeSkillSet>false</tga:IncludeSkillSet>
                    <tga:IncludeAccreditedCourse>false</tga:IncludeAccreditedCourse>
                    <tga:IncludeAccreditedCourseModule>false</tga:IncludeAccreditedCourseModule>
                    <tga:IncludeTrainingPackage>false</tga:IncludeTrainingPackage>
                    <tga:IncludeUnitContextualisation>false</tga:IncludeUnitContextualisation>
                </tga:TrainingComponentTypes>
            </tga:request>
        </tga:Search>"""
        
        response = self._make_soap_request('training', 'Search', body)
        if response:
            return self._parse_search_response(response)
        return None

    def _parse_search_response(self, response: ET.Element) -> Dict:
        """Parse training component search response"""
        result = {
            'total_count': 0,
            'page_number': 1,
            'page_size': 50,
            'units': []
        }
        
        try:
            # Get pagination info
            count_elem = response.find('.//Count')
            if count_elem is not None and count_elem.text:
                result['total_count'] = int(count_elem.text)
                
            page_num_elem = response.find('.//PageNumber')
            if page_num_elem is not None and page_num_elem.text:
                result['page_number'] = int(page_num_elem.text)
                
            page_size_elem = response.find('.//PageSize')
            if page_size_elem is not None and page_size_elem.text:
                result['page_size'] = int(page_size_elem.text)
            
            # Parse unit results
            for result_elem in response.findall('.//Results'):
                unit = {}
                for child in result_elem:
                    unit[child.tag] = child.text if child.text else ""
                
                if unit:  # Only add non-empty units
                    result['units'].append(unit)
                    
        except Exception as e:
            logger.error(f"Error parsing search response: {e}")
            
        return result

    def _parse_unit_details(self, response: ET.Element) -> Dict:
        """Parse detailed unit information"""
        unit = {}
        
        try:
            # Find the main training component element
            details_elem = response.find('.//GetDetailsResult')
            if details_elem is None:
                return unit
                
            # Basic information
            for child in details_elem:
                if child.tag in ['Code', 'Title', 'ComponentType', 'CreatedDate', 'UpdatedDate', 'IsConfidential', 'CurrencyStatus']:
                    unit[child.tag] = child.text if child.text else ""
                elif child.tag == 'CurrencyPeriods':
                    unit['currency_periods'] = self._parse_currency_periods(child)
                elif child.tag == 'Classifications':
                    unit['classifications'] = self._parse_classifications(child)
                elif child.tag == 'Releases':
                    unit['releases'] = self._parse_releases(child)
                elif child.tag == 'IndustrySectors':
                    unit['industry_sectors'] = self._parse_industry_sectors(child)
                elif child.tag == 'Occupations':
                    unit['occupations'] = self._parse_occupations(child)
                elif child.tag == 'MappingInformation':
                    unit['mapping_info'] = self._parse_mapping_info(child)
                    
        except Exception as e:
            logger.error(f"Error parsing unit details: {e}")
            
        return unit

    def _parse_currency_periods(self, elem: ET.Element) -> List[Dict]:
        """Parse currency periods"""
        periods = []
        for period_elem in elem.findall('.//NrtCurrencyPeriod'):
            period = {}
            for child in period_elem:
                period[child.tag] = child.text if child.text else ""
            periods.append(period)
        return periods

    def _parse_classifications(self, elem: ET.Element) -> List[Dict]:
        """Parse classifications"""
        classifications = []
        for class_elem in elem.findall('.//Classification'):
            classification = {}
            for child in class_elem:
                classification[child.tag] = child.text if child.text else ""
            classifications.append(classification)
        return classifications

    def _parse_releases(self, elem: ET.Element) -> List[Dict]:
        """Parse releases"""
        releases = []
        for release_elem in elem.findall('.//Release'):
            release = {}
            for child in release_elem:
                if child.tag in ['ReleaseNumber', 'ReleaseDate', 'Currency']:
                    release[child.tag] = child.text if child.text else ""
            releases.append(release)
        return releases

    def _parse_industry_sectors(self, elem: ET.Element) -> List[Dict]:
        """Parse industry sectors"""
        sectors = []
        for sector_elem in elem.findall('.//TrainingComponentIndustrySector'):
            sector = {}
            for child in sector_elem:
                sector[child.tag] = child.text if child.text else ""
            sectors.append(sector)
        return sectors

    def _parse_occupations(self, elem: ET.Element) -> List[Dict]:
        """Parse occupations"""
        occupations = []
        for occ_elem in elem.findall('.//TrainingComponentOccupation'):
            occupation = {}
            for child in occ_elem:
                occupation[child.tag] = child.text if child.text else ""
            occupations.append(occupation)
        return occupations

    def _parse_mapping_info(self, elem: ET.Element) -> List[Dict]:
        """Parse mapping information"""
        mappings = []
        for map_elem in elem.findall('.//Mapping'):
            mapping = {}
            for child in map_elem:
                mapping[child.tag] = child.text if child.text else ""
            mappings.append(mapping)
        return mappings

    def get_popular_training_packages(self) -> List[str]:
        """Get list of popular training package codes for filtering"""
        # These are some common training packages in Australia
        return [
            'BSB', 'SIT', 'CHC', 'ICT', 'TLI', 'MSL', 'FNS', 'HLT', 
            'CPC', 'ELE', 'UEE', 'AUR', 'MEM', 'RII', 'AHC', 'FSK'
        ]

# Example usage function
def test_tga_connection():
    """Test the TGA connection and search functionality"""
    client = TGAClient()
    
    print("Testing TGA connection...")
    if client.test_connection():
        print("✓ Successfully connected to TGA services")
        
        # Test server time
        server_time = client.get_server_time()
        if server_time:
            print(f"✓ Server time: {server_time}")
        
        # Test searching for units
        print("\nSearching for Communication units...")
        results = client.search_units_of_competency("communication", page_size=5)
        if results and results['units']:
            print(f"✓ Found {results['total_count']} units")
            for unit in results['units'][:3]:
                print(f"  - {unit.get('Code', 'N/A')}: {unit.get('Title', 'N/A')}")
                
            # Test getting details for first unit
            first_unit_code = results['units'][0].get('Code')
            if first_unit_code:
                print(f"\nGetting details for {first_unit_code}...")
                details = client.get_unit_details(first_unit_code)
                if details:
                    print(f"✓ Retrieved details for {details.get('Title', 'Unknown')}")
                    if details.get('currency_periods'):
                        print(f"  - Currency periods: {len(details['currency_periods'])}")
                    if details.get('industry_sectors'):
                        print(f"  - Industry sectors: {len(details['industry_sectors'])}")
        else:
            print("✗ No units found or search failed")
    else:
        print("✗ Failed to connect to TGA services")

if __name__ == "__main__":
    test_tga_connection()
