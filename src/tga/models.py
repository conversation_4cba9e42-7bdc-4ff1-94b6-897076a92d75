"""
Django models for TGA Training Components
"""

from django.db import models
from django.utils import timezone
import json

class TrainingPackage(models.Model):
    code = models.CharField(max_length=20, unique=True, db_index=True)
    title = models.CharField(max_length=500)
    description = models.TextField(blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    tga_created_date = models.DateTimeField(null=True, blank=True)
    tga_updated_date = models.DateTimeField(null=True, blank=True)
    is_current = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'tga_training_packages'
        verbose_name = 'Training Package'
        verbose_name_plural = 'Training Packages'
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.title}"

class UnitOfCompetency(models.Model):
    COMPONENT_TYPE_CHOICES = [
        ('Unit', 'Unit of Competency'),
        ('UnitContextualisation', 'Unit Contextualisation'),
    ]
    
    CURRENCY_STATUS_CHOICES = [
        ('Current', 'Current'),
        ('Superseded', 'Superseded'),
        ('Deleted', 'Deleted'),
        ('Unknown', 'Unknown'),
    ]
    
    code = models.CharField(max_length=20, unique=True, db_index=True)
    title = models.CharField(max_length=500, db_index=True)
    component_type = models.CharField(max_length=50, choices=COMPONENT_TYPE_CHOICES, default='Unit')
    parent_training_package = models.ForeignKey(
        TrainingPackage, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='units'
    )
    
    # TGA specific fields
    tga_created_date = models.DateTimeField(null=True, blank=True)
    tga_updated_date = models.DateTimeField(null=True, blank=True)
    currency_status = models.CharField(max_length=20, choices=CURRENCY_STATUS_CHOICES, default='Current')
    is_confidential = models.BooleanField(default=False)
    
    # Local tracking
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    last_synced = models.DateTimeField(null=True, blank=True)
    
    # Search optimization
    search_text = models.TextField(blank=True, help_text="Combined searchable text")
    
    class Meta:
        db_table = 'tga_units_of_competency'
        verbose_name = 'Unit of Competency'
        verbose_name_plural = 'Units of Competency'
        ordering = ['code']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['title']),
            models.Index(fields=['currency_status']),
            models.Index(fields=['parent_training_package']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.title}"
    
    def save(self, *args, **kwargs):
        # Update search text
        self.search_text = f"{self.code} {self.title}".lower()
        super().save(*args, **kwargs)

class UnitCurrencyPeriod(models.Model):
    unit = models.ForeignKey(UnitOfCompetency, on_delete=models.CASCADE, related_name='currency_periods')
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    authority = models.CharField(max_length=200, blank=True)
    end_reason_code = models.CharField(max_length=10, blank=True)
    end_comment = models.TextField(blank=True)
    
    class Meta:
        db_table = 'tga_unit_currency_periods'
        ordering = ['-start_date']

class UnitClassification(models.Model):
    unit = models.ForeignKey(UnitOfCompetency, on_delete=models.CASCADE, related_name='classifications')
    purpose_code = models.CharField(max_length=10)
    scheme_code = models.CharField(max_length=10)
    value_code = models.CharField(max_length=10)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    
    class Meta:
        db_table = 'tga_unit_classifications'

class IndustrySector(models.Model):
    code = models.CharField(max_length=20, unique=True, db_index=True)
    title = models.CharField(max_length=300)
    description = models.TextField(blank=True)
    parent_code = models.CharField(max_length=20, blank=True)
    
    class Meta:
        db_table = 'tga_industry_sectors'
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.title}"

class Occupation(models.Model):
    code = models.CharField(max_length=20, unique=True, db_index=True)
    title = models.CharField(max_length=300)
    description = models.TextField(blank=True)
    
    class Meta:
        db_table = 'tga_occupations'
        ordering = ['code']
    
    def __str__(self):
        return f"{self.code} - {self.title}"

class UnitIndustrySector(models.Model):
    unit = models.ForeignKey(UnitOfCompetency, on_delete=models.CASCADE, related_name='industry_sectors')
    industry_sector = models.ForeignKey(IndustrySector, on_delete=models.CASCADE)
    
    class Meta:
        db_table = 'tga_unit_industry_sectors'
        unique_together = ['unit', 'industry_sector']

class UnitOccupation(models.Model):
    unit = models.ForeignKey(UnitOfCompetency, on_delete=models.CASCADE, related_name='occupations')
    occupation = models.ForeignKey(Occupation, on_delete=models.CASCADE)
    
    class Meta:
        db_table = 'tga_unit_occupations'
        unique_together = ['unit', 'occupation']

class UnitRelease(models.Model):
    unit = models.ForeignKey(UnitOfCompetency, on_delete=models.CASCADE, related_name='releases')
    release_number = models.CharField(max_length=20)
    release_date = models.DateField()
    currency = models.CharField(max_length=20)  # Current, Replaced, etc.
    
    class Meta:
        db_table = 'tga_unit_releases'
        ordering = ['-release_date']

class UnitMapping(models.Model):
    """Tracks supersession and equivalency relationships between units"""
    unit = models.ForeignKey(UnitOfCompetency, on_delete=models.CASCADE, related_name='mapping_from')
    maps_to_code = models.CharField(max_length=20)
    maps_to_title = models.CharField(max_length=500)
    is_equivalent = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'tga_unit_mappings'

class SearchQuery(models.Model):
    """Track user search queries for analytics"""
    query_text = models.CharField(max_length=500)
    results_count = models.IntegerField(default=0)
    search_timestamp = models.DateTimeField(auto_now_add=True)
    user_ip = models.GenericIPAddressField(null=True, blank=True)
    
    class Meta:
        db_table = 'tga_search_queries'
        ordering = ['-search_timestamp']

class SyncLog(models.Model):
    """Track TGA data synchronization"""
    sync_type = models.CharField(max_length=50)  # 'full', 'incremental', 'unit_details'
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    records_processed = models.IntegerField(default=0)
    records_updated = models.IntegerField(default=0)
    records_created = models.IntegerField(default=0)
    errors_count = models.IntegerField(default=0)
    error_details = models.TextField(blank=True)
    status = models.CharField(max_length=20, default='running')  # running, completed, failed
    
    class Meta:
        db_table = 'tga_sync_logs'
        ordering = ['-start_time']
    
    def __str__(self):
        return f"{self.sync_type} sync at {self.start_time}"
