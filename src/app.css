:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

/* Custom styles for quiz platform */
.quiz-choice-card {
  flex: 1;
  height: 12rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 0.5rem;
}

.quiz-choice-card:hover {
  transform: scale(1.05);
}

.quiz-choice-card:nth-child(1) {
  background-color: #f472b6;
}

.quiz-choice-card:nth-child(2) {
  background-color: #60a5fa;
}

.quiz-choice-card:nth-child(3) {
  background-color: #facc15;
}

.quiz-choice-card:nth-child(4) {
  background-color: #a78bfa;
}

.quiz-choice-card.correct {
  background-color: #4ade80;
}

.quiz-choice-card.incorrect {
  background-color: #f87171;
}

.clock {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  border: 4px solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.game-code {
  font-size: 3.75rem;
  font-weight: bold;
  letter-spacing: 0.1em;
  text-align: center;
  padding: 2rem;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
}

.player-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  padding: 1rem;
}

.player-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.leaderboard {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.leaderboard-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.leaderboard-entry:last-child {
  border-bottom: none;
}

.leaderboard-entry:nth-child(1) {
  background-color: #fef3c7;
  font-weight: bold;
}

.leaderboard-entry:nth-child(2) {
  background-color: #f3f4f6;
}

.leaderboard-entry:nth-child(3) {
  background-color: #fed7aa;
}
