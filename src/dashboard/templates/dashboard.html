<!-- templates/tga_search/dashboard.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TGA Units of Competency Search</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .search-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
        }
        .result-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.8em;
        }
        .loading-spinner {
            display: none;
        }
        .search-stats {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header Section -->
        <div class="search-container">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center mb-4">
                            <h1 class="display-4 mb-3">
                                <i class="fas fa-search me-3"></i>
                                TGA Units Search
                            </h1>
                            <p class="lead">Search Australian Training.gov.au Units of Competency</p>
                        </div>

                        <!-- Search Form -->
                        <form method="post" id="search-form">
                            {% csrf_token %}
                            <div class="card">
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="{{ form.search_term.id_for_label }}" class="form-label">
                                                <i class="fas fa-book me-2"></i>{{ form.search_term.label }}
                                            </label>
                                            {{ form.search_term }}
                                        </div>
                                        <div class="col-md-3">
                                            <label for="{{ form.environment.id_for_label }}" class="form-label">
                                                <i class="fas fa-server me-2"></i>{{ form.environment.label }}
                                            </label>
                                            {{ form.environment }}
                                        </div>
                                        <div class="col-md-3 d-flex align-items-end">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <span class="search-text">
                                                    <i class="fas fa-search me-2"></i>Search
                                                </span>
                                                <span class="loading-spinner">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>Searching...
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <div class="form-check">
                                                {{ form.include_superseded }}
                                                <label class="form-check-label" for="{{ form.include_superseded.id_for_label }}">
                                                    {{ form.include_superseded.label }}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="container mt-4">
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="row">
                <!-- Search Results -->
                <div class="col-lg-8">
                    {% if search_results %}
                        <div class="search-stats">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                                        Search Results
                                    </h5>
                                    <p class="text-muted mb-0">Found {{ total_count }} units of competency</p>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        Last updated: {% now "Y-m-d H:i" %}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="results-container">
                            {% for unit in search_results %}
                                <div class="col-12 mb-3">
                                    <div class="card result-card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <h5 class="card-title text-primary">
                                                        <i class="fas fa-certificate me-2"></i>
                                                        {{ unit.code|default:"No Code" }}
                                                    </h5>
                                                    <p class="card-text">{{ unit.title }}</p>
                                                    <small class="text-muted">
                                                        <i class="fas fa-tag me-1"></i>
                                                        Type: {{ unit.component_type|default:"Unit" }}
                                                    </small>
                                                </div>
                                                <div class="col-md-4 text-md-end">
                                                    <div class="mb-2">
                                                        {% if unit.currency_status == "Current" %}
                                                            <span class="badge bg-success status-badge">
                                                                <i class="fas fa-check-circle me-1"></i>Current
                                                            </span>
                                                        {% elif unit.currency_status == "Superseded" %}
                                                            <span class="badge bg-warning status-badge">
                                                                <i class="fas fa-exclamation-triangle me-1"></i>Superseded
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-secondary status-badge">
                                                                <i class="fas fa-question-circle me-1"></i>{{ unit.currency_status|default:"Unknown" }}
                                                            </span>
                                                        {% endif %}
                                                    </div>
                                                    {% if unit.is_streamlined == "true" %}
                                                        <div class="mb-2">
                                                            <span class="badge bg-info status-badge">
                                                                <i class="fas fa-star me-1"></i>Streamlined
                                                            </span>
                                                        </div>
                                                    {% endif %}
                                                    {% if unit.created_date %}
                                                        <small class="text-muted d-block">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            Created: {{ unit.created_date|date:"Y-m-d" }}
                                                        </small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No Search Results</h4>
                            <p class="text-muted">Enter a search term above to find units of competency</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Search Tips -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-lightbulb me-2"></i>Search Tips
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Search by unit code: <code>BSBCMM411</code>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Search by keyword: <code>communication</code>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Search by training package: <code>BSB</code>
                                </li>
                                <li>
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Use sandbox for testing
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Recent Searches -->
                    {% if recent_searches %}
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-history me-2"></i>Recent Searches
                                </h6>
                            </div>
                            <div class="card-body">
                                {% for search in recent_searches %}
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-truncate me-2">{{ search.search_term }}</small>
                                        <span class="badge bg-light text-dark">{{ search.results_count }}</span>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    <!-- API Information -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>API Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="small text-muted mb-2">
                                <strong>Environment:</strong> {{ form.environment.value|default:"sandbox" }}
                            </p>
                            <p class="small text-muted mb-2">
                                <strong>Service:</strong> TGA Web Services V12
                            </p>
                            <p class="small text-muted mb-0">
                                <strong>Authentication:</strong> WS-Security
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('search-form');
            const searchButton = form.querySelector('button[type="submit"]');
            const searchText = searchButton.querySelector('.search-text');
            const loadingSpinner = searchButton.querySelector('.loading-spinner');

            form.addEventListener('submit', function() {
                // Show loading state
                searchText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
                searchButton.disabled = true;
            });

            // Auto-focus search input
            const searchInput = document.querySelector('input[name="search_term"]');
            if (searchInput) {
                searchInput.focus();
            }

            // Add keyboard shortcut (Ctrl+K or Cmd+K to focus search)
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    searchInput.focus();
                }
            });
        });
    </script>
</body>
</html>