
#src/dashboard/views.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required




# Create your views here.
# views.py
from django.shortcuts import render
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import requests
import xml.etree.ElementTree as ET
from xml.sax.saxutils import escape
import base64
from .forms import UnitSearchForm
from .models import TGASearchLog


class TGAWebServiceClient:
	"""Client for TGA Web Services based on the specification document"""

	def __init__(self, environment='sandbox'):
		self.environment = environment
		self.endpoints = {'production': 'https://ws.training.gov.au/Deewr.Tga.Webservices/TrainingComponentServiceV12.svc', 'sandbox': 'https://ws.sandbox.training.gov.au/Deewr.Tga.Webservices/TrainingComponentServiceV12.svc', 'staging': 'https://ws.staging.training.gov.au/Deewr.Tga.Webservices/TrainingComponentServiceV12.svc'}

		# Credentials from the document
		self.username = 'WebService.Read'
		self.password = 'Asdf098'

		self.base_url = self.endpoints[environment]

	def create_soap_envelope(self, body_content):
		"""Create SOAP envelope with WS-Security authentication"""
		soap_envelope = f"""<?xml version="1.0" encoding="utf-8"?>
<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" 
               xmlns:tga="http://training.gov.au/services/12/">
    <soap:Header>
        <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
            <wsse:UsernameToken>
                <wsse:Username>{self.username}</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password>
            </wsse:UsernameToken>
        </wsse:Security>
    </soap:Header>
    <soap:Body>
        {body_content}
    </soap:Body>
</soap:Envelope>"""
		return soap_envelope

	def search_training_components(self, search_term, include_superseded=False, include_deleted=False):
		"""Search for training components (units of competency)"""

		# Create the search request body based on the specification
		body_content = f"""
        <tga:Search>
            <tga:request>
                <tga:Filter>{escape(search_term)}</tga:Filter>
                <tga:SearchCode>true</tga:SearchCode>
                <tga:SearchTitle>true</tga:SearchTitle>
                <tga:SearchIndustrySector>false</tga:SearchIndustrySector>
                <tga:SearchOccupation>false</tga:SearchOccupation>
                <tga:IncludeSuperseded>{str(include_superseded).lower()}</tga:IncludeSuperseded>
                <tga:IncludeDeleted>{str(include_deleted).lower()}</tga:IncludeDeleted>
                <tga:PageNumber>1</tga:PageNumber>
                <tga:PageSize>20</tga:PageSize>
                <tga:TrainingComponentTypes>
                    <tga:IncludeUnit>true</tga:IncludeUnit>
                    <tga:IncludeQualification>false</tga:IncludeQualification>
                    <tga:IncludeSkillSet>false</tga:IncludeSkillSet>
                    <tga:IncludeTrainingPackage>false</tga:IncludeTrainingPackage>
                    <tga:IncludeAccreditedCourse>false</tga:IncludeAccreditedCourse>
                    <tga:IncludeAccreditedCourseModule>false</tga:IncludeAccreditedCourseModule>
                    <tga:IncludeUnitContextualisation>false</tga:IncludeUnitContextualisation>
                </tga:TrainingComponentTypes>
                <tga:IncludeLegacyData>false</tga:IncludeLegacyData>
            </tga:request>
        </tga:Search>
        """

		soap_envelope = self.create_soap_envelope(body_content)

		headers = {'Content-Type': 'text/xml; charset=utf-8', 'SOAPAction': 'http://training.gov.au/services/12/ITrainingComponentService/Search'}

		try:
			response = requests.post(f"{self.base_url}/Training",  # SOAP 1.1 endpoint
				data=soap_envelope, headers=headers, timeout=30)

			if response.status_code == 200:
				return self.parse_search_response(response.text)
			else:
				return {'success': False, 'error': f'HTTP {response.status_code}: {response.text[:500]}'}

		except requests.exceptions.RequestException as e:
			return {'success': False, 'error': f'Request failed: {str(e)}'}

	def parse_search_response(self, xml_response):
		"""Parse the SOAP response and extract training component data"""
		try:
			# Remove namespace prefixes for easier parsing
			clean_xml = xml_response.replace('soap:', '').replace('tga:', '')

			root = ET.fromstring(clean_xml)

			# Find the search result
			results = []

			# Look for TrainingComponentSearchResult in the response
			for result_elem in root.findall('.//Results'):
				for item in result_elem.findall('.//TrainingComponentSummary'):
					unit_data = {'code': self.get_element_text(item, 'Code'), 'title': self.get_element_text(item, 'Title'), 'component_type': self.get_element_text(item, 'ComponentType'), 'currency_status': self.get_element_text(item, 'CurrencyStatus'), 'created_date': self.get_element_text(item, 'CreatedDate'), 'updated_date': self.get_element_text(item, 'UpdatedDate'),
						'is_current': self.get_element_text(item, 'IsCurrent'), 'is_legacy_data': self.get_element_text(item, 'IsLegacyData'), 'is_streamlined': self.get_element_text(item, 'IsStreamlined'), 'usage_recommendation': self.get_element_text(item, 'UsageRecommendation')}
					results.append(unit_data)

			# Get total count
			count_elem = root.find('.//Count')
			total_count = int(count_elem.text) if count_elem is not None else len(results)

			return {'success': True, 'results': results, 'total_count': total_count}

		except ET.ParseError as e:
			return {'success': False, 'error': f'XML parsing error: {str(e)}'}
		except Exception as e:
			return {'success': False, 'error': f'Unexpected error: {str(e)}'}

	def get_element_text(self, parent, tag_name):
		"""Safely get text content from an XML element"""
		elem = parent.find(tag_name)
		return elem.text if elem is not None else ''


def dashboard_view(request):
	"""Main dashboard view for searching units of competency"""
	form = UnitSearchForm()
	search_results = []
	total_count = 0

	if request.method == 'POST':
		form = UnitSearchForm(request.POST)
		if form.is_valid():
			search_term = form.cleaned_data['search_term']
			environment = form.cleaned_data['environment']
			include_superseded = form.cleaned_data['include_superseded']

			# Initialize TGA client
			client = TGAWebServiceClient(environment)

			# Perform search
			result = client.search_training_components(search_term=search_term, include_superseded=include_superseded)

			if result['success']:
				search_results = result['results']
				total_count = result['total_count']

				# Log the search
				TGASearchLog.objects.create(search_term=search_term, results_count=total_count, environment=environment)

				messages.success(request, f'Found {total_count} units of competency')
			else:
				messages.error(request, f'Search failed: {result["error"]}')

	context = {'form': form, 'search_results': search_results, 'total_count': total_count, 'recent_searches': TGASearchLog.objects.all().order_by('-search_date')[:5]}

	return render(request, 'dashboard/dashboard.html', context)


@csrf_exempt
def ajax_search(request):
	"""AJAX endpoint for real-time search"""
	if request.method == 'POST':
		search_term = request.POST.get('search_term', '')
		environment = request.POST.get('environment', 'sandbox')

		if len(search_term) < 3:
			return JsonResponse({'success': False, 'error': 'Search term must be at least 3 characters'})

		client = TGAWebServiceClient(environment)
		result = client.search_training_components(search_term)

		return JsonResponse(result)

	return JsonResponse({'success': False, 'error': 'Invalid request method'})