# Generated by Django 5.2.1 on 2025-07-30 04:22

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TGASearchLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('search_term', models.Char<PERSON>ield(max_length=255)),
                ('results_count', models.IntegerField(default=0)),
                ('search_date', models.DateTimeField(auto_now_add=True)),
                ('environment', models.Char<PERSON>ield(default='sandbox', max_length=20)),
            ],
        ),
    ]
