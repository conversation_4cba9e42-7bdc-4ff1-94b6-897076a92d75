from django.core.management.base import BaseCommand
from django.conf import settings
import sys
import os

# Add the app directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from views import TGAWebServiceClient


class Command(BaseCommand):
	help = 'Test TGA Web Services API connection and search functionality'

	def add_arguments(self, parser):
		parser.add_argument('--environment', type=str, default='sandbox', choices=['sandbox', 'staging', 'production'], help='TGA environment to test (default: sandbox)')
		parser.add_argument('--search-term', type=str, default='BSB', help='Search term to test (default: BSB)')
		parser.add_argument('--verbose', action='store_true', help='Show detailed output')

	def handle(self, *args, **options):
		environment = options['environment']
		search_term = options['search_term']
		verbose = options['verbose']

		self.stdout.write(f"Testing TGA API connection...")
		self.stdout.write(f"Environment: {environment}")
		self.stdout.write(f"Search term: {search_term}")
		self.stdout.write("-" * 50)

		try:
			# Initialize client
			client = TGAWebServiceClient(environment)

			if verbose:
				self.stdout.write(f"Endpoint: {client.base_url}")
				self.stdout.write(f"Username: {client.username}")
				self.stdout.write("-" * 50)

			# Test search
			self.stdout.write("Performing search...")
			result = client.search_training_components(search_term)

			if result['success']:
				self.stdout.write(self.style.SUCCESS(f"✓ Search successful!"))
				self.stdout.write(f"Total results: {result['total_count']}")

				if verbose and result['results']:
					self.stdout.write("\nFirst 5 results:")
					for i, unit in enumerate(result['results'][:5], 1):
						self.stdout.write(f"{i}. {unit['code']} - {unit['title']}")
						self.stdout.write(f"   Status: {unit['currency_status']}")
						self.stdout.write(f"   Type: {unit['component_type']}")
						self.stdout.write("")

			else:
				self.stdout.write(self.style.ERROR(f"✗ Search failed: {result['error']}"))

		except Exception as e:
			self.stdout.write(self.style.ERROR(f"✗ Unexpected error: {str(e)}"))