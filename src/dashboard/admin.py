# admin.py
from django.contrib import admin
from .models import TGASearchLog


@admin.register(TGASearchLog)
class TGASearchLogAdmin(admin.ModelAdmin):
	list_display = ('search_term', 'results_count', 'environment', 'search_date')
	list_filter = ('environment', 'search_date')
	search_fields = ('search_term',)
	readonly_fields = ('search_date',)
	ordering = ('-search_date',)

	def has_add_permission(self, request):
		return False  # Prevent manual addition