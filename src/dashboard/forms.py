# forms.py
from django import forms


class UnitSearchForm(forms.Form):
	ENVIRONMENT_CHOICES = [('sandbox', 'Sandbox'), ('staging', 'Staging'), ('production', 'Production'), ]

	search_term = forms.CharField(max_length=255, widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter unit code or title (e.g., BSB, communication, BSBCMM411)', 'autocomplete': 'off'}), label='Search Units of Competency')

	environment = forms.ChoiceField(choices=ENVIRONMENT_CHOICES, initial='sandbox', widget=forms.Select(attrs={'class': 'form-select'}), label='TGA Environment')

	include_superseded = forms.BooleanField(required=False, initial=False, widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}), label='Include superseded units')