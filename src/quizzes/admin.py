from django.contrib import admin
from .models import Quiz, QuizQuestion, QuizChoice

# Register your models here.
class QuizChoiceInline(admin.TabularInline):
    model = QuizChoice
    extra = 4  # Show 4 empty forms for new choices

class QuizQuestionInline(admin.TabularInline):
    model = QuizQuestion
    extra = 1  # Show 1 empty form for a new question

class QuizQuestionAdmin(admin.ModelAdmin):
    inlines = [QuizChoiceInline]
    list_display = ('name', 'quiz', 'time', 'order')
    list_filter = ('quiz',)
    search_fields = ('name',)

class QuizAdmin(admin.ModelAdmin):
    inlines = [QuizQuestionInline]
    list_display = ('name', 'created_at', 'updated_at')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')

# Register the models with their admin classes
admin.site.register(Quiz, QuizAdmin)
admin.site.register(QuizQuestion, QuizQuestionAdmin)
