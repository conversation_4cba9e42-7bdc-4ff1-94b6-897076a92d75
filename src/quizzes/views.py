from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Quiz, QuizQuestion, QuizChoice
from .serializers import QuizSerializer, QuizQuestionSerializer, QuizChoiceSerializer

# Create your views here.
class QuizViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows quizzes to be viewed or edited.
    """
    queryset = Quiz.objects.all().order_by('-created_at')
    serializer_class = QuizSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        """
        Optionally restricts the returned quizzes to those owned by the current user,
        by filtering against a `user` query parameter in the URL.
        """
        queryset = Quiz.objects.all().order_by('-created_at')
        # If we implement user ownership of quizzes, we can filter here
        return queryset
    
    @action(detail=False, methods=['get'])
    def my_quizzes(self, request):
        """
        Return a list of all quizzes created by the current user.
        """
        # This is a placeholder for future user-specific filtering
        quizzes = self.get_queryset()
        serializer = self.get_serializer(quizzes, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def questions(self, request, pk=None):
        """
        Return a list of all questions for a specific quiz.
        """
        quiz = self.get_object()
        questions = quiz.questions.all().order_by('order')
        serializer = QuizQuestionSerializer(questions, many=True)
        return Response(serializer.data)

class QuizQuestionViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows quiz questions to be viewed or edited.
    """
    queryset = QuizQuestion.objects.all()
    serializer_class = QuizQuestionSerializer
    permission_classes = [permissions.AllowAny]
    
    def get_queryset(self):
        """
        Optionally restricts the returned questions to those belonging to a specific quiz,
        by filtering against a `quiz` query parameter in the URL.
        """
        queryset = QuizQuestion.objects.all()
        quiz_id = self.request.query_params.get('quiz', None)
        if quiz_id is not None:
            queryset = queryset.filter(quiz__id=quiz_id)
        return queryset
    
    @action(detail=True, methods=['get'])
    def choices(self, request, pk=None):
        """
        Return a list of all choices for a specific question.
        """
        question = self.get_object()
        choices = question.choices.all()
        serializer = QuizChoiceSerializer(choices, many=True)
        return Response(serializer.data)
