from django.core.management.base import BaseCommand
from quizzes.models import Quiz, QuizQuestion, QuizChoice


class Command(BaseCommand):
    help = 'Create sample quiz data for testing'

    def handle(self, *args, **options):
        # Create a sample quiz
        quiz = Quiz.objects.create(
            name="General Knowledge Quiz"
        )

        # Question 1
        question1 = QuizQuestion.objects.create(
            quiz=quiz,
            name="What is the capital of France?",
            time=30,
            order=0
        )
        
        QuizChoice.objects.create(
            question=question1,
            name="London",
            correct=False
        )
        QuizChoice.objects.create(
            question=question1,
            name="Paris",
            correct=True
        )
        QuizChoice.objects.create(
            question=question1,
            name="Berlin",
            correct=False
        )
        QuizChoice.objects.create(
            question=question1,
            name="Madrid",
            correct=False
        )

        # Question 2
        question2 = QuizQuestion.objects.create(
            quiz=quiz,
            name="Which planet is known as the Red Planet?",
            time=25,
            order=1
        )
        
        QuizChoice.objects.create(
            question=question2,
            name="Venus",
            correct=False
        )
        QuizChoice.objects.create(
            question=question2,
            name="Mars",
            correct=True
        )
        QuizChoice.objects.create(
            question=question2,
            name="Jupiter",
            correct=False
        )
        QuizChoice.objects.create(
            question=question2,
            name="Saturn",
            correct=False
        )

        # Question 3
        question3 = QuizQuestion.objects.create(
            quiz=quiz,
            name="What is 2 + 2?",
            time=15,
            order=2
        )
        
        QuizChoice.objects.create(
            question=question3,
            name="3",
            correct=False
        )
        QuizChoice.objects.create(
            question=question3,
            name="4",
            correct=True
        )
        QuizChoice.objects.create(
            question=question3,
            name="5",
            correct=False
        )
        QuizChoice.objects.create(
            question=question3,
            name="6",
            correct=False
        )

        # Create another quiz
        quiz2 = Quiz.objects.create(
            name="Science Quiz"
        )

        # Science Question 1
        science_q1 = QuizQuestion.objects.create(
            quiz=quiz2,
            name="What is the chemical symbol for water?",
            time=20,
            order=0
        )
        
        QuizChoice.objects.create(
            question=science_q1,
            name="H2O",
            correct=True
        )
        QuizChoice.objects.create(
            question=science_q1,
            name="CO2",
            correct=False
        )
        QuizChoice.objects.create(
            question=science_q1,
            name="NaCl",
            correct=False
        )
        QuizChoice.objects.create(
            question=science_q1,
            name="O2",
            correct=False
        )

        # Science Question 2
        science_q2 = QuizQuestion.objects.create(
            quiz=quiz2,
            name="How many bones are in the human body?",
            time=30,
            order=1
        )
        
        QuizChoice.objects.create(
            question=science_q2,
            name="206",
            correct=True
        )
        QuizChoice.objects.create(
            question=science_q2,
            name="208",
            correct=False
        )
        QuizChoice.objects.create(
            question=science_q2,
            name="204",
            correct=False
        )
        QuizChoice.objects.create(
            question=science_q2,
            name="210",
            correct=False
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample data:\n'
                f'- Quiz: "{quiz.name}" with {quiz.questions.count()} questions\n'
                f'- Quiz: "{quiz2.name}" with {quiz2.questions.count()} questions'
            )
        )
