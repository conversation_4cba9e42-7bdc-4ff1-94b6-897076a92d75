from django.db import models
import uuid

# Create your models here.
class Quiz(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.name

class QuizQuestion(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    quiz = models.ForeignKey(Quiz, related_name='questions', on_delete=models.CASCADE)
    name = models.Char<PERSON>ield(max_length=255)
    time = models.IntegerField(default=30)  # Time in seconds
    order = models.IntegerField(default=0)
    
    class Meta:
        ordering = ['order']
    
    def __str__(self):
        return self.name

class QuizChoice(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    question = models.Foreign<PERSON>ey(QuizQuestion, related_name='choices', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    correct = models.BooleanField(default=False)
    
    def __str__(self):
        return self.name
