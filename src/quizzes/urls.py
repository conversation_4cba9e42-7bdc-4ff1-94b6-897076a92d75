from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import QuizViewSet, QuizQuestionViewSet

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'quizzes', QuizViewSet)
router.register(r'questions', QuizQuestionViewSet)

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
]