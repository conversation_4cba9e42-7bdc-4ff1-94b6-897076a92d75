from rest_framework import serializers
from .models import Quiz, QuizQuestion, QuizChoice

class QuizChoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = QuizChoice
        fields = ['id', 'name', 'correct']

class QuizQuestionSerializer(serializers.ModelSerializer):
    choices = QuizChoiceSerializer(many=True, read_only=False)
    
    class Meta:
        model = QuizQuestion
        fields = ['id', 'name', 'time', 'order', 'choices']
    
    def create(self, validated_data):
        choices_data = validated_data.pop('choices')
        question = QuizQuestion.objects.create(**validated_data)
        for choice_data in choices_data:
            QuizChoice.objects.create(question=question, **choice_data)
        return question
    
    def update(self, instance, validated_data):
        choices_data = validated_data.pop('choices', None)
        instance.name = validated_data.get('name', instance.name)
        instance.time = validated_data.get('time', instance.time)
        instance.order = validated_data.get('order', instance.order)
        instance.save()
        
        if choices_data is not None:
            # Delete existing choices and create new ones
            instance.choices.all().delete()
            for choice_data in choices_data:
                QuizChoice.objects.create(question=instance, **choice_data)
        
        return instance

class QuizSerializer(serializers.ModelSerializer):
    questions = QuizQuestionSerializer(many=True, read_only=False)
    
    class Meta:
        model = Quiz
        fields = ['id', 'name', 'created_at', 'updated_at', 'questions']
        read_only_fields = ['created_at', 'updated_at']
    
    def create(self, validated_data):
        questions_data = validated_data.pop('questions')
        quiz = Quiz.objects.create(**validated_data)
        for question_data in questions_data:
            choices_data = question_data.pop('choices')
            question = QuizQuestion.objects.create(quiz=quiz, **question_data)
            for choice_data in choices_data:
                QuizChoice.objects.create(question=question, **choice_data)
        return quiz
    
    def update(self, instance, validated_data):
        questions_data = validated_data.pop('questions', None)
        instance.name = validated_data.get('name', instance.name)
        instance.save()
        
        if questions_data is not None:
            # Delete existing questions and create new ones
            instance.questions.all().delete()
            for question_data in questions_data:
                choices_data = question_data.pop('choices')
                question = QuizQuestion.objects.create(quiz=instance, **question_data)
                for choice_data in choices_data:
                    QuizChoice.objects.create(question=question, **choice_data)
        
        return instance