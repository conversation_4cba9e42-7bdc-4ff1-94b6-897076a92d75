/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@import url("https://fonts.googleapis.com/css2?family=Arsenal+SC:ital,wght@0,400;0,700;1,400;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100..900;1,100..900&family=Raleway:ital,wght@0,100..900;1,100..900&display=swap");
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Con<PERSON><PERSON>, "Liberation Mono",
      "Courier New", monospace;
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-md: 28rem;
    --container-2xl: 42rem;
    --container-4xl: 56rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --radius-sm: 0.25rem;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --font-roboto: "Roboto", sans-serif;
    --font-poppins: "Poppins", sans-serif;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .modal {
    pointer-events: none;
    visibility: hidden;
    position: fixed;
    inset: calc(0.25rem * 0);
    margin: calc(0.25rem * 0);
    display: grid;
    height: 100%;
    max-height: none;
    width: 100%;
    max-width: none;
    align-items: center;
    justify-items: center;
    background-color: transparent;
    padding: calc(0.25rem * 0);
    color: inherit;
    overflow-x: hidden;
    transition: translate 0.3s ease-out, visibility 0.3s allow-discrete, background-color 0.3s ease-out, opacity 0.1s ease-out;
    overflow-y: hidden;
    overscroll-behavior: contain;
    z-index: 999;
    &::backdrop {
      display: none;
    }
    &.modal-open, &[open], &:target {
      pointer-events: auto;
      visibility: visible;
      opacity: 100%;
      background-color: oklch(0% 0 0/ 0.4);
      .modal-box {
        translate: 0 0;
        scale: 1;
        opacity: 1;
      }
    }
    @starting-style {
      &.modal-open, &[open], &:target {
        visibility: hidden;
        opacity: 0%;
      }
    }
  }
  .menu {
    display: flex;
    width: fit-content;
    flex-direction: column;
    flex-wrap: wrap;
    padding: calc(0.25rem * 2);
    --menu-active-fg: var(--color-neutral-content);
    --menu-active-bg: var(--color-neutral);
    font-size: 0.875rem;
    :where(li ul) {
      position: relative;
      margin-inline-start: calc(0.25rem * 4);
      padding-inline-start: calc(0.25rem * 2);
      white-space: nowrap;
      &:before {
        position: absolute;
        inset-inline-start: calc(0.25rem * 0);
        top: calc(0.25rem * 3);
        bottom: calc(0.25rem * 3);
        background-color: var(--color-base-content);
        opacity: 10%;
        width: var(--border);
        content: "";
      }
    }
    :where(li > .menu-dropdown:not(.menu-dropdown-show)) {
      display: none;
    }
    :where(li:not(.menu-title) > *:not(ul, details, .menu-title, .btn)), :where(li:not(.menu-title) > details > summary:not(.menu-title)) {
      display: grid;
      grid-auto-flow: column;
      align-content: flex-start;
      align-items: center;
      gap: calc(0.25rem * 2);
      border-radius: var(--radius-field);
      padding-inline: calc(0.25rem * 3);
      padding-block: calc(0.25rem * 1.5);
      text-align: start;
      transition-property: color, background-color, box-shadow;
      transition-duration: 0.2s;
      transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
      grid-auto-columns: minmax(auto, max-content) auto max-content;
      text-wrap: balance;
      user-select: none;
    }
    :where(li > details > summary) {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      &::-webkit-details-marker {
        display: none;
      }
    }
    :where(li > details > summary), :where(li > .menu-dropdown-toggle) {
      &:after {
        justify-self: flex-end;
        display: block;
        height: 0.375rem;
        width: 0.375rem;
        rotate: -135deg;
        translate: 0 -1px;
        transition-property: rotate, translate;
        transition-duration: 0.2s;
        content: "";
        transform-origin: 50% 50%;
        box-shadow: 2px 2px inset;
        pointer-events: none;
      }
    }
    :where(li > details[open] > summary):after, :where(li > .menu-dropdown-toggle.menu-dropdown-show):after {
      rotate: 45deg;
      translate: 0 1px;
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title), li:not(.menu-title, .disabled) > details > summary:not(.menu-title) ):not(.menu-active, :active, .btn) {
      &.menu-focus, &:focus-visible {
        cursor: pointer;
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        color: var(--color-base-content);
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where( li:not(.menu-title, .disabled) > *:not(ul, details, .menu-title):not(.menu-active, :active, .btn):hover, li:not(.menu-title, .disabled) > details > summary:not(.menu-title):not(.menu-active, :active, .btn):hover ) {
      cursor: pointer;
      background-color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
      }
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
      box-shadow: 0 1px oklch(0% 0 0 / 0.01) inset, 0 -1px oklch(100% 0 0 / 0.01) inset;
    }
    :where(li:empty) {
      background-color: var(--color-base-content);
      opacity: 10%;
      margin: 0.5rem 1rem;
      height: 1px;
    }
    :where(li) {
      position: relative;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      flex-wrap: wrap;
      align-items: stretch;
      .badge {
        justify-self: flex-end;
      }
      & > *:not(ul, .menu-title, details, .btn):active, & > *:not(ul, .menu-title, details, .btn).menu-active, & > details > summary:active {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
        color: var(--menu-active-fg);
        background-color: var(--menu-active-bg);
        background-size: auto, calc(var(--noise) * 100%);
        background-image: none, var(--fx-noise);
        &:not(&:active) {
          box-shadow: 0 2px calc(var(--depth) * 3px) -2px var(--menu-active-bg);
        }
      }
      &.menu-disabled {
        pointer-events: none;
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
    }
    .dropdown:focus-within {
      .menu-dropdown-toggle:after {
        rotate: 45deg;
        translate: 0 1px;
      }
    }
    .dropdown-content {
      margin-top: calc(0.25rem * 2);
      padding: calc(0.25rem * 2);
      &:before {
        display: none;
      }
    }
  }
  .btn {
    :where(&) {
      width: unset;
    }
    display: inline-flex;
    flex-shrink: 0;
    cursor: pointer;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 1.5);
    text-align: center;
    vertical-align: middle;
    outline-offset: 2px;
    webkit-user-select: none;
    user-select: none;
    padding-inline: var(--btn-p);
    color: var(--btn-fg);
    --tw-prose-links: var(--btn-fg);
    height: var(--size);
    font-size: var(--fontsize, 0.875rem);
    font-weight: 600;
    outline-color: var(--btn-color, var(--color-base-content));
    transition-property: color, background-color, border-color, box-shadow;
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
    transition-duration: 0.2s;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    background-color: var(--btn-bg);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--btn-noise);
    border-width: var(--border);
    border-style: solid;
    border-color: var(--btn-border);
    text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
    touch-action: manipulation;
    box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
    --size: calc(var(--size-field, 0.25rem) * 10);
    --btn-bg: var(--btn-color, var(--color-base-200));
    --btn-fg: var(--color-base-content);
    --btn-p: 1rem;
    --btn-border: var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
    }
    --btn-shadow: 0 3px 2px -2px var(--btn-bg),
    0 4px 3px -2px var(--btn-bg);
    @supports (color: color-mix(in lab, red, red)) {
      --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),
    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
    }
    --btn-noise: var(--fx-noise);
    .prose & {
      text-decoration-line: none;
    }
    @media (hover: hover) {
      &:hover {
        --btn-bg: var(--btn-color, var(--color-base-200));
        @supports (color: color-mix(in lab, red, red)) {
          --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
        }
      }
    }
    &:focus-visible {
      outline-width: 2px;
      outline-style: solid;
      isolation: isolate;
    }
    &:active:not(.btn-active) {
      translate: 0 0.5px;
      --btn-bg: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
      }
      --btn-border: var(--btn-color, var(--color-base-200));
      @supports (color: color-mix(in lab, red, red)) {
        --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
      }
      --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
    }
    &:is(:disabled, [disabled], .btn-disabled) {
      &:not(.btn-link, .btn-ghost) {
        background-color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
        }
        box-shadow: none;
      }
      pointer-events: none;
      --btn-border: #0000;
      --btn-noise: none;
      --btn-fg: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
      }
      @media (hover: hover) {
        &:hover {
          pointer-events: none;
          background-color: var(--color-neutral);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
          }
          --btn-border: #0000;
          --btn-fg: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
          }
        }
      }
    }
    &:is(input[type="checkbox"], input[type="radio"]) {
      appearance: none;
      &::after {
        content: attr(aria-label);
      }
    }
    &:where(input:checked:not(.filter .btn)) {
      --btn-color: var(--color-primary);
      --btn-fg: var(--color-primary-content);
      isolation: isolate;
    }
  }
  .list {
    display: flex;
    flex-direction: column;
    font-size: 0.875rem;
    :where(.list-row) {
      --list-grid-cols: minmax(0, auto) 1fr;
      position: relative;
      display: grid;
      grid-auto-flow: column;
      gap: calc(0.25rem * 4);
      border-radius: var(--radius-box);
      padding: calc(0.25rem * 4);
      word-break: break-word;
      grid-template-columns: var(--list-grid-cols);
      &:has(.list-col-grow:nth-child(1)) {
        --list-grid-cols: 1fr;
      }
      &:has(.list-col-grow:nth-child(2)) {
        --list-grid-cols: minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(3)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(4)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(5)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
      }
      &:has(.list-col-grow:nth-child(6)) {
        --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto)
        minmax(0, auto) 1fr;
      }
      :not(.list-col-wrap) {
        grid-row-start: 1;
      }
    }
    & > :not(:last-child) {
      &.list-row, .list-row {
        &:after {
          content: "";
          border-bottom: var(--border) solid;
          inset-inline: var(--radius-box);
          position: absolute;
          bottom: calc(0.25rem * 0);
          border-color: var(--color-base-content);
          @supports (color: color-mix(in lab, red, red)) {
            border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
          }
        }
      }
    }
  }
  .toggle {
    border: var(--border) solid currentColor;
    color: var(--input-color);
    position: relative;
    display: inline-grid;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    place-content: center;
    vertical-align: middle;
    webkit-user-select: none;
    user-select: none;
    grid-template-columns: 0fr 1fr 1fr;
    --radius-selector-max: calc(
    var(--radius-selector) + var(--radius-selector) + var(--radius-selector)
  );
    border-radius: calc( var(--radius-selector) + min(var(--toggle-p), var(--radius-selector-max)) + min(var(--border), var(--radius-selector-max)) );
    padding: var(--toggle-p);
    box-shadow: 0 1px currentColor inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000) inset;
    }
    transition: color 0.3s, grid-template-columns 0.2s;
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 50%, #0000);
    }
    --toggle-p: calc(var(--size) * 0.125);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: calc((var(--size) * 2) - (var(--border) + var(--toggle-p)) * 2);
    height: var(--size);
    > * {
      z-index: 1;
      grid-column: span 1 / span 1;
      grid-column-start: 2;
      grid-row-start: 1;
      height: 100%;
      cursor: pointer;
      appearance: none;
      background-color: transparent;
      padding: calc(0.25rem * 0.5);
      transition: opacity 0.2s, rotate 0.4s;
      border: none;
      &:focus {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
      &:nth-child(2) {
        color: var(--color-base-100);
        rotate: 0deg;
      }
      &:nth-child(3) {
        color: var(--color-base-100);
        opacity: 0%;
        rotate: -15deg;
      }
    }
    &:has(:checked) {
      > :nth-child(2) {
        opacity: 0%;
        rotate: 15deg;
      }
      > :nth-child(3) {
        opacity: 100%;
        rotate: 0deg;
      }
    }
    &:before {
      position: relative;
      inset-inline-start: calc(0.25rem * 0);
      grid-column-start: 2;
      grid-row-start: 1;
      aspect-ratio: 1 / 1;
      height: 100%;
      border-radius: var(--radius-selector);
      background-color: currentColor;
      translate: 0;
      --tw-content: "";
      content: var(--tw-content);
      transition: background-color 0.1s, translate 0.2s, inset-inline-start 0.2s;
      box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px currentColor;
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 -1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px color-mix(in oklab, currentColor calc(var(--depth) * 10%), #0000);
      }
      background-size: auto, calc(var(--noise) * 100%);
      background-image: none, var(--fx-noise);
    }
    @media (forced-colors: active) {
      &:before {
        outline-style: var(--tw-outline-style);
        outline-width: 1px;
        outline-offset: calc(1px * -1);
      }
    }
    @media print {
      &:before {
        outline: 0.25rem solid;
        outline-offset: -1rem;
      }
    }
    &:focus-visible, &:has(:focus-visible) {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"], &:has(> input:checked) {
      grid-template-columns: 1fr 1fr 0fr;
      background-color: var(--color-base-100);
      --input-color: var(--color-base-content);
      &:before {
        background-color: currentColor;
      }
      @starting-style {
        &:before {
          opacity: 0;
        }
      }
    }
    &:indeterminate {
      grid-template-columns: 0.5fr 1fr 0.5fr;
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 30%;
      &:before {
        background-color: transparent;
        border: var(--border) solid currentColor;
      }
    }
  }
  .input {
    cursor: text;
    border: var(--border) solid #0000;
    position: relative;
    display: inline-flex;
    flex-shrink: 1;
    appearance: none;
    align-items: center;
    gap: calc(0.25rem * 2);
    background-color: var(--color-base-100);
    padding-inline: calc(0.25rem * 3);
    vertical-align: middle;
    white-space: nowrap;
    width: clamp(3rem, 20rem, 100%);
    height: var(--size);
    font-size: 0.875rem;
    touch-action: manipulation;
    border-start-start-radius: var(--join-ss, var(--radius-field));
    border-start-end-radius: var(--join-se, var(--radius-field));
    border-end-start-radius: var(--join-es, var(--radius-field));
    border-end-end-radius: var(--join-ee, var(--radius-field));
    border-color: var(--input-color);
    box-shadow: 0 1px var(--input-color) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
    }
    --size: calc(var(--size-field, 0.25rem) * 10);
    --input-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
    }
    &:where(input) {
      display: inline-flex;
    }
    :where(input) {
      display: inline-flex;
      height: 100%;
      width: 100%;
      appearance: none;
      background-color: transparent;
      border: none;
      &:focus, &:focus-within {
        --tw-outline-style: none;
        outline-style: none;
        @media (forced-colors: active) {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }
    }
    :where(input[type="url"]), :where(input[type="email"]) {
      direction: ltr;
    }
    :where(input[type="date"]) {
      display: inline-block;
    }
    &:focus, &:focus-within {
      --input-color: var(--color-base-content);
      box-shadow: 0 1px var(--input-color);
      @supports (color: color-mix(in lab, red, red)) {
        box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
      }
      outline: 2px solid var(--input-color);
      outline-offset: 2px;
      isolation: isolate;
      z-index: 1;
    }
    &:has(> input[disabled]), &:is(:disabled, [disabled]) {
      cursor: not-allowed;
      border-color: var(--color-base-200);
      background-color: var(--color-base-200);
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
      }
      &::placeholder {
        color: var(--color-base-content);
        @supports (color: color-mix(in lab, red, red)) {
          color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
        }
      }
      box-shadow: none;
    }
    &:has(> input[disabled]) > input[disabled] {
      cursor: not-allowed;
    }
    &::-webkit-date-and-time-value {
      text-align: inherit;
    }
    &[type="number"] {
      &::-webkit-inner-spin-button {
        margin-block: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * -3);
      }
    }
    &::-webkit-calendar-picker-indicator {
      position: absolute;
      inset-inline-end: 0.75em;
    }
  }
  .table {
    font-size: 0.875rem;
    position: relative;
    width: 100%;
    border-radius: var(--radius-box);
    text-align: left;
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
    tr.row-hover {
      &, &:nth-child(even) {
        &:hover {
          @media (hover: hover) {
            background-color: var(--color-base-200);
          }
        }
      }
    }
    :where(th, td) {
      padding-inline: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 3);
      vertical-align: middle;
    }
    :where(thead, tfoot) {
      white-space: nowrap;
      color: var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
      }
      font-size: 0.875rem;
      font-weight: 600;
    }
    :where(tfoot) {
      border-top: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-top: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
    :where(.table-pin-rows thead tr) {
      position: sticky;
      top: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-rows tfoot tr) {
      position: sticky;
      bottom: calc(0.25rem * 0);
      z-index: 1;
      background-color: var(--color-base-100);
    }
    :where(.table-pin-cols tr th) {
      position: sticky;
      right: calc(0.25rem * 0);
      left: calc(0.25rem * 0);
      background-color: var(--color-base-100);
    }
    :where(thead tr, tbody tr:not(:last-child)) {
      border-bottom: var(--border) solid var(--color-base-content);
      @supports (color: color-mix(in lab, red, red)) {
        border-bottom: var(--border) solid color-mix(in oklch, var(--color-base-content) 5%, #0000);
      }
    }
  }
  .menu-horizontal {
    display: inline-flex;
    flex-direction: row;
    & > li:not(.menu-title) > details > ul {
      position: absolute;
      margin-inline-start: calc(0.25rem * 0);
      margin-top: calc(0.25rem * 4);
      padding-block: calc(0.25rem * 2);
      padding-inline-end: calc(0.25rem * 2);
    }
    & > li > details > ul {
      &:before {
        content: none;
      }
    }
    :where(& > li:not(.menu-title) > details > ul) {
      border-radius: var(--radius-box);
      background-color: var(--color-base-100);
      box-shadow: 0 1px 3px 0 oklch(0% 0 0/0.1), 0 1px 2px -1px oklch(0% 0 0/0.1);
    }
  }
  .avatar {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
    & > div {
      display: block;
      aspect-ratio: 1 / 1;
      overflow: hidden;
    }
    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
  .checkbox {
    border: var(--border) solid var(--input-color, var(--color-base-content));
    @supports (color: color-mix(in lab, red, red)) {
      border: var(--border) solid var(--input-color, color-mix(in oklab, var(--color-base-content) 20%, #0000));
    }
    position: relative;
    display: inline-block;
    flex-shrink: 0;
    cursor: pointer;
    appearance: none;
    border-radius: var(--radius-selector);
    padding: calc(0.25rem * 1);
    vertical-align: middle;
    color: var(--color-base-content);
    box-shadow: 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1)) inset, 0 0 #0000 inset, 0 0 #0000;
    transition: background-color 0.2s, box-shadow 0.2s;
    --size: calc(var(--size-selector, 0.25rem) * 6);
    width: var(--size);
    height: var(--size);
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    &:before {
      --tw-content: "";
      content: var(--tw-content);
      display: block;
      width: 100%;
      height: 100%;
      rotate: 45deg;
      background-color: currentColor;
      opacity: 0%;
      transition: clip-path 0.3s, opacity 0.1s, rotate 0.3s, translate 0.3s;
      transition-delay: 0.1s;
      clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 70% 80%, 70% 100%);
      box-shadow: 0px 3px 0 0px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
      font-size: 1rem;
      line-height: 0.75;
    }
    &:focus-visible {
      outline: 2px solid var(--input-color, currentColor);
      outline-offset: 2px;
    }
    &:checked, &[aria-checked="true"] {
      background-color: var(--input-color, #0000);
      box-shadow: 0 0 #0000 inset, 0 8px 0 -4px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px oklch(0% 0 0 / calc(var(--depth) * 0.1));
      &:before {
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 0%, 70% 0%, 70% 100%);
        opacity: 100%;
      }
      @media (forced-colors: active) {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
      @media print {
        &:before {
          rotate: 0deg;
          background-color: transparent;
          --tw-content: "✔︎";
          clip-path: none;
        }
      }
    }
    &:indeterminate {
      &:before {
        rotate: 0deg;
        opacity: 100%;
        translate: 0 -35%;
        clip-path: polygon(20% 100%, 20% 80%, 50% 80%, 50% 80%, 80% 80%, 80% 100%);
      }
    }
    &:disabled {
      cursor: not-allowed;
      opacity: 20%;
    }
  }
  .absolute {
    position: absolute;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-1 {
    right: calc(var(--spacing) * 1);
  }
  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }
  .modal-box {
    grid-column-start: 1;
    grid-row-start: 1;
    max-height: 100vh;
    width: calc(11/12 * 100%);
    max-width: 32rem;
    background-color: var(--color-base-100);
    padding: calc(0.25rem * 6);
    transition: translate 0.3s ease-out, scale 0.3s ease-out, opacity 0.2s ease-out 0.05s, box-shadow 0.3s ease-out;
    border-top-left-radius: var(--modal-tl, var(--radius-box));
    border-top-right-radius: var(--modal-tr, var(--radius-box));
    border-bottom-left-radius: var(--modal-bl, var(--radius-box));
    border-bottom-right-radius: var(--modal-br, var(--radius-box));
    scale: 95%;
    opacity: 0;
    box-shadow: oklch(0% 0 0/ 0.25) 0px 25px 50px -12px;
    overflow-y: auto;
    overscroll-behavior: contain;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .divider {
    display: flex;
    height: calc(0.25rem * 4);
    flex-direction: row;
    align-items: center;
    align-self: stretch;
    white-space: nowrap;
    margin: var(--divider-m, 1rem 0);
    --divider-color: var(--color-base-content);
    @supports (color: color-mix(in lab, red, red)) {
      --divider-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
    }
    &:before, &:after {
      content: "";
      height: calc(0.25rem * 0.5);
      width: 100%;
      flex-grow: 1;
      background-color: var(--divider-color);
    }
    @media print {
      &:before, &:after {
        border: 0.5px solid;
      }
    }
    &:not(:empty) {
      gap: calc(0.25rem * 4);
    }
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .label {
    display: inline-flex;
    align-items: center;
    gap: calc(0.25rem * 1.5);
    white-space: nowrap;
    color: currentColor;
    @supports (color: color-mix(in lab, red, red)) {
      color: color-mix(in oklab, currentColor 60%, transparent);
    }
    &:has(input) {
      cursor: pointer;
    }
    &:is(.input > *, .select > *) {
      display: flex;
      height: calc(100% - 0.5rem);
      align-items: center;
      padding-inline: calc(0.25rem * 3);
      white-space: nowrap;
      font-size: inherit;
      &:first-child {
        margin-inline-start: calc(0.25rem * -3);
        margin-inline-end: calc(0.25rem * 3);
        border-inline-end: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
      &:last-child {
        margin-inline-start: calc(0.25rem * 3);
        margin-inline-end: calc(0.25rem * -3);
        border-inline-start: var(--border) solid currentColor;
        @supports (color: color-mix(in lab, red, red)) {
          border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
        }
      }
    }
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: calc(0.25rem * 2);
    border-radius: var(--radius-selector);
    vertical-align: middle;
    color: var(--badge-fg);
    border: var(--border) solid var(--badge-color, var(--color-base-200));
    font-size: 0.875rem;
    width: fit-content;
    padding-inline: calc(0.25rem * 3 - var(--border));
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    background-color: var(--badge-bg);
    --badge-bg: var(--badge-color, var(--color-base-100));
    --badge-fg: var(--color-base-content);
    --size: calc(var(--size-selector, 0.25rem) * 6);
    height: var(--size);
  }
  .navbar {
    display: flex;
    width: 100%;
    align-items: center;
    padding: 0.5rem;
    min-height: 4rem;
  }
  .footer {
    display: grid;
    width: 100%;
    grid-auto-flow: row;
    place-items: start;
    column-gap: calc(0.25rem * 4);
    row-gap: calc(0.25rem * 10);
    font-size: 0.875rem;
    line-height: 1.25rem;
    & > * {
      display: grid;
      place-items: start;
      gap: calc(0.25rem * 2);
    }
    &.footer-center {
      grid-auto-flow: column dense;
      place-items: center;
      text-align: center;
      & > * {
        place-items: center;
      }
    }
  }
  .alert {
    display: grid;
    align-items: center;
    gap: calc(0.25rem * 4);
    border-radius: var(--radius-box);
    padding-inline: calc(0.25rem * 4);
    padding-block: calc(0.25rem * 3);
    color: var(--color-base-content);
    background-color: var(--alert-color, var(--color-base-200));
    justify-content: start;
    justify-items: start;
    grid-auto-flow: column;
    grid-template-columns: auto;
    text-align: start;
    border: var(--border) solid var(--color-base-200);
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-size: auto, calc(var(--noise) * 100%);
    background-image: none, var(--fx-noise);
    box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px #000, 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));
    @supports (color: color-mix(in lab, red, red)) {
      box-shadow: 0 3px 0 -2px oklch(100% 0 0 / calc(var(--depth) * 0.08)) inset, 0 1px color-mix( in oklab, color-mix(in oklab, #000 20%, var(--alert-color, var(--color-base-200))) calc(var(--depth) * 20%), #0000 ), 0 4px 3px -2px oklch(0% 0 0 / calc(var(--depth) * 0.08));
    }
    &:has(:nth-child(2)) {
      grid-template-columns: auto minmax(auto, 1fr);
    }
    &.alert-outline {
      background-color: transparent;
      color: var(--alert-color);
      box-shadow: none;
      background-image: none;
    }
    &.alert-dash {
      background-color: transparent;
      color: var(--alert-color);
      border-style: dashed;
      box-shadow: none;
      background-image: none;
    }
    &.alert-soft {
      color: var(--alert-color, var(--color-base-content));
      background: var(--alert-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        background: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 8%, var(--color-base-100) );
      }
      border-color: var(--alert-color, var(--color-base-content));
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix( in oklab, var(--alert-color, var(--color-base-content)) 10%, var(--color-base-100) );
      }
      box-shadow: none;
      background-image: none;
    }
  }
  .block {
    display: block;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .table {
    display: table;
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-full {
    height: 100%;
  }
  .min-h-\[48px\] {
    min-height: 48px;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-full {
    width: 100%;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .flex-1 {
    flex: 1;
  }
  .flex-none {
    flex: none;
  }
  .link {
    cursor: pointer;
    text-decoration-line: underline;
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
      @media (forced-colors: active) {
        outline: 2px solid transparent;
        outline-offset: 2px;
      }
    }
    &:focus-visible {
      outline: 2px solid currentColor;
      outline-offset: 2px;
    }
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-disc {
    list-style-type: disc;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-box {
    border-radius: var(--radius-box);
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-1 {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .alert-error {
    border-color: var(--color-error);
    color: var(--color-error-content);
    --alert-color: var(--color-error);
  }
  .alert-info {
    border-color: var(--color-info);
    color: var(--color-info-content);
    --alert-color: var(--color-info);
  }
  .alert-success {
    border-color: var(--color-success);
    color: var(--color-success-content);
    --alert-color: var(--color-success);
  }
  .alert-warning {
    border-color: var(--color-warning);
    color: var(--color-warning-content);
    --alert-color: var(--color-warning);
  }
  .border-base-300 {
    border-color: var(--color-base-300);
  }
  .border-black {
    border-color: var(--color-black);
  }
  .border-neutral-content {
    border-color: var(--color-neutral-content);
  }
  .table-zebra {
    tbody {
      tr {
        &:where(:nth-child(even)) {
          background-color: var(--color-base-200);
          :where(.table-pin-cols tr th) {
            background-color: var(--color-base-200);
          }
        }
        &.row-hover {
          &, &:where(:nth-child(even)) {
            &:hover {
              @media (hover: hover) {
                background-color: var(--color-base-300);
              }
            }
          }
        }
      }
    }
  }
  .bg-base-100 {
    background-color: var(--color-base-100);
  }
  .bg-base-200 {
    background-color: var(--color-base-200);
  }
  .bg-info\/20 {
    background-color: var(--color-info);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-info) 20%, transparent);
    }
  }
  .bg-neutral {
    background-color: var(--color-neutral);
  }
  .bg-primary {
    background-color: var(--color-primary);
  }
  .bg-success\/20 {
    background-color: var(--color-success);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-success) 20%, transparent);
    }
  }
  .bg-warning\/20 {
    background-color: var(--color-warning);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-warning) 20%, transparent);
    }
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .align-top {
    vertical-align: top;
  }
  .font-poppins {
    font-family: var(--font-poppins);
  }
  .font-roboto {
    font-family: var(--font-roboto);
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .break-all {
    word-break: break-all;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .checkbox-primary {
    color: var(--color-primary-content);
    --input-color: var(--color-primary);
  }
  .text-accent-content {
    color: var(--color-accent-content);
  }
  .text-base-content {
    color: var(--color-base-content);
  }
  .text-black {
    color: var(--color-black);
  }
  .text-error {
    color: var(--color-error);
  }
  .text-info {
    color: var(--color-info);
  }
  .text-neutral-content {
    color: var(--color-neutral-content);
  }
  .text-primary {
    color: var(--color-primary);
  }
  .text-secondary {
    color: var(--color-secondary);
  }
  .text-warning {
    color: var(--color-warning);
  }
  .text-white {
    color: var(--color-white);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .btn-ghost {
    &:not(.btn-active, :hover, :active:focus, :focus-visible) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-border: #0000;
      --btn-noise: none;
      &:not(:disabled, [disabled], .btn-disabled) {
        outline-color: currentColor;
        --btn-fg: currentColor;
      }
    }
    @media (hover: none) {
      &:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-border: #0000;
        --btn-noise: none;
        --btn-fg: currentColor;
      }
    }
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .btn-outline {
    &:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
      --btn-shadow: "";
      --btn-bg: #0000;
      --btn-fg: var(--btn-color);
      --btn-border: var(--btn-color);
      --btn-noise: none;
    }
    @media (hover: none) {
      &:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
        --btn-shadow: "";
        --btn-bg: #0000;
        --btn-fg: var(--btn-color);
        --btn-border: var(--btn-color);
        --btn-noise: none;
      }
    }
  }
  .btn-sm {
    --fontsize: 0.75rem;
    --btn-p: 0.75rem;
    --size: calc(var(--size-field, 0.25rem) * 8);
  }
  .badge-primary {
    --badge-color: var(--color-primary);
    --badge-fg: var(--color-primary-content);
  }
  .badge-success {
    --badge-color: var(--color-success);
    --badge-fg: var(--color-success-content);
  }
  .badge-warning {
    --badge-color: var(--color-warning);
    --badge-fg: var(--color-warning-content);
  }
  .btn-error {
    --btn-color: var(--color-error);
    --btn-fg: var(--color-error-content);
  }
  .btn-primary {
    --btn-color: var(--color-primary);
    --btn-fg: var(--color-primary-content);
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .input-error {
    &, &:focus, &:focus-within {
      --input-color: var(--color-error);
    }
  }
  .toggle-primary {
    &:checked, &[aria-checked="true"] {
      --input-color: var(--color-primary);
    }
  }
  .toggle-sm {
    &:is([type="checkbox"]), &:has([type="checkbox"]) {
      --size: calc(var(--size-selector, 0.25rem) * 5);
    }
  }
  .group-hover\:text-red-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-base-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-base-200);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100);
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .focus\:border-neutral-content {
    &:focus {
      border-color: var(--color-neutral-content);
    }
  }
  .focus\:ring-0 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .md\:col-span-1 {
    @media (width >= 48rem) {
      grid-column: span 1 / span 1;
    }
  }
  .md\:col-span-2 {
    @media (width >= 48rem) {
      grid-column: span 2 / span 2;
    }
  }
  .md\:mx-16 {
    @media (width >= 48rem) {
      margin-inline: calc(var(--spacing) * 16);
    }
  }
  .md\:mt-8 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .md\:mb-6 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .md\:mb-16 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }
  .md\:w-16 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 16);
    }
  }
  .md\:max-w-\[320px\] {
    @media (width >= 48rem) {
      max-width: 320px;
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:space-y-6 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:text-3xl {
    @media (width >= 48rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-lg {
    @media (width >= 48rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .md\:text-sm {
    @media (width >= 48rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .lg\:max-w-\[480px\] {
    @media (width >= 64rem) {
      max-width: 480px;
    }
  }
}
.my-h1 {
  margin-bottom: calc(var(--spacing) * 2);
  font-family: var(--font-roboto);
  font-size: var(--text-lg);
  line-height: var(--tw-leading, var(--text-lg--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  @media (width >= 48rem) {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
}
.my-h2 {
  font-family: var(--font-roboto);
  font-size: var(--text-base);
  line-height: var(--tw-leading, var(--text-base--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  @media (width >= 48rem) {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
}
.my-h3 {
  font-family: var(--font-roboto);
  font-size: var(--text-sm);
  line-height: var(--tw-leading, var(--text-sm--line-height));
  --tw-font-weight: var(--font-weight-medium);
  font-weight: var(--font-weight-medium);
  @media (width >= 48rem) {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
}
.my-p {
  font-family: var(--font-poppins);
  font-size: var(--text-xs);
  line-height: var(--tw-leading, var(--text-xs--line-height));
  @media (width >= 48rem) {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
}
.my-input {
  width: 100%;
  border-radius: var(--radius-sm);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-neutral-content);
  padding: calc(var(--spacing) * 6);
  padding-right: calc(var(--spacing) * 10);
  --tw-outline-style: none;
  outline-style: none;
  &:focus {
    border-color: var(--color-neutral-content);
  }
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  &:focus {
    --tw-outline-style: none;
    outline-style: none;
  }
}
.my-card {
  :where(& > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }
  border-radius: var(--radius-sm);
  border-style: var(--tw-border-style);
  border-width: 1px;
  border-color: var(--color-base-300);
  background-color: var(--color-base-100);
  padding: calc(var(--spacing) * 4);
  --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  @media (width >= 48rem) {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  @media (width >= 48rem) {
    padding: calc(var(--spacing) * 6);
  }
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  @media (prefers-color-scheme: dark) {
    :root {
      color-scheme: dark;
      --color-base-100: oklch(25.33% 0.016 252.42);
      --color-base-200: oklch(23.26% 0.014 253.1);
      --color-base-300: oklch(21.15% 0.012 254.09);
      --color-base-content: oklch(97.807% 0.029 256.847);
      --color-primary: oklch(58% 0.233 277.117);
      --color-primary-content: oklch(96% 0.018 272.314);
      --color-secondary: oklch(65% 0.241 354.308);
      --color-secondary-content: oklch(94% 0.028 342.258);
      --color-accent: oklch(77% 0.152 181.912);
      --color-accent-content: oklch(38% 0.063 188.416);
      --color-neutral: oklch(14% 0.005 285.823);
      --color-neutral-content: oklch(92% 0.004 286.32);
      --color-info: oklch(74% 0.16 232.661);
      --color-info-content: oklch(29% 0.066 243.157);
      --color-success: oklch(76% 0.177 163.223);
      --color-success-content: oklch(37% 0.077 168.94);
      --color-warning: oklch(82% 0.189 84.429);
      --color-warning-content: oklch(41% 0.112 45.904);
      --color-error: oklch(71% 0.194 13.428);
      --color-error-content: oklch(27% 0.105 12.094);
      --radius-selector: 0.5rem;
      --radius-field: 0.25rem;
      --radius-box: 0.5rem;
      --size-selector: 0.25rem;
      --size-field: 0.25rem;
      --border: 1px;
      --depth: 1;
      --noise: 0;
    }
  }
}
@layer base {
  :root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(45% 0.24 277.023);
    --color-primary-content: oklch(93% 0.034 272.788);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {
    color-scheme: dark;
    --color-base-100: oklch(25.33% 0.016 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root {
    --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
  }
}
@layer base {
  :root, [data-theme] {
    background-color: var(--root-bg, var(--color-base-100));
    color: var(--color-base-content);
  }
}
@layer base {
  :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not([class*="drawer-open"]) > .drawer-toggle:checked ) {
    overflow: hidden;
  }
}
@layer base {
  @property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
  }
}
@layer base {
  :where( :root:has( .modal-open, .modal[open], .modal:target, .modal-toggle:checked, .drawer:not(.drawer-open) > .drawer-toggle:checked ) ) {
    scrollbar-gutter: stable;
    background-image: linear-gradient(var(--color-base-100), var(--color-base-100));
    --root-bg: var(--color-base-100);
    @supports (color: color-mix(in lab, red, red)) {
      --root-bg: color-mix(in srgb, var(--color-base-100), oklch(0% 0 0) 40%);
    }
  }
  :where(.modal[open], .modal-open, .modal-toggle:checked + .modal):not(.modal-start, .modal-end) {
    scrollbar-gutter: stable;
  }
}
@layer base {
  :root {
    scrollbar-color: currentColor #0000;
    @supports (color: color-mix(in lab, red, red)) {
      scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
    }
  }
}
@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}
@keyframes rating {
  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
@keyframes skeleton {
  0% {
    background-position: 150%;
  }
  100% {
    background-position: -50%;
  }
}
@keyframes dropdown {
  0% {
    opacity: 0;
  }
}
@keyframes radio {
  0% {
    padding: 5px;
  }
  50% {
    padding: 3px;
  }
}
@keyframes toast {
  0% {
    scale: 0.9;
    opacity: 0;
  }
  100% {
    scale: 1;
    opacity: 1;
  }
}
@layer base {
  :where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme="light"] {
    color-scheme: light;
    --color-base-100: oklch(98% 0.016 73.684);
    --color-base-200: oklch(98% 0.022 95.277);
    --color-base-300: oklch(98% 0.026 102.212);
    --color-base-content: oklch(27.807% 0.029 256.847);
    --color-primary: oklch(39% 0.09 240.876);
    --color-primary-content: oklch(98% 0.003 247.858);
    --color-secondary: oklch(53.92% 0.162 241.36);
    --color-secondary-content: oklch(90.784% 0.032 241.36);
    --color-accent: oklch(75.98% 0.204 56.72);
    --color-accent-content: oklch(0.697 0.005 78.29);
    --color-neutral: oklch(0.937 0 89.876);
    --color-neutral-content: oklch(0.74 0.003 228.798);
    --color-info: oklch(72.06% 0.191 231.6);
    --color-info-content: oklch(0% 0 0);
    --color-success: oklch(0.826 0.189 124.775);
    --color-success-content: oklch(0% 0 0);
    --color-warning: oklch(84.71% 0.199 83.87);
    --color-warning-content: oklch(0% 0 0);
    --color-error: oklch(71.76% 0.221 22.18);
    --color-error-content: oklch(0% 0 0);
    --radius-selector: 0.25rem;
    --radius-field: 0.25rem;
    --radius-box: 0.25rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@layer base {
  :root:has(input.theme-controller[value=dark]:checked),[data-theme="dark"] {
    color-scheme: dark;
    --color-base-100: oklch(20.768% 0.039 265.754);
    --color-base-200: oklch(19.314% 0.037 265.754);
    --color-base-300: oklch(17.86% 0.034 265.754);
    --color-base-content: oklch(84.153% 0.007 265.754);
    --color-primary: oklch(75.351% 0.138 232.661);
    --color-primary-content: oklch(15.07% 0.027 232.661);
    --color-secondary: oklch(68.011% 0.158 276.934);
    --color-secondary-content: oklch(13.602% 0.031 276.934);
    --color-accent: oklch(72.36% 0.176 350.048);
    --color-accent-content: oklch(14.472% 0.035 350.048);
    --color-neutral: oklch(27.949% 0.036 260.03);
    --color-neutral-content: oklch(85.589% 0.007 260.03);
    --color-info: oklch(68.455% 0.148 237.251);
    --color-info-content: oklch(0% 0 0);
    --color-success: oklch(78.452% 0.132 181.911);
    --color-success-content: oklch(15.69% 0.026 181.911);
    --color-warning: oklch(83.242% 0.139 82.95);
    --color-warning-content: oklch(16.648% 0.027 82.95);
    --color-error: oklch(71.785% 0.17 13.118);
    --color-error-content: oklch(14.357% 0.034 13.118);
    --radius-selector: 0.25rem;
    --radius-field: 0.25rem;
    --radius-box: 0.25rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
    }
  }
}
