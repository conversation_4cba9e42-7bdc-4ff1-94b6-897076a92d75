<script lang="ts">
  import { push } from 'svelte-spa-router';

  // Svelte 5 runes for state management
  let gameCode = $state('');
  let playerName = $state('');
  let isJoining = $state(false);

  function navigateToHost() {
    push('/host');
  }

  function navigateToQuizzes() {
    push('/quizzes');
  }

  function handleJoinGame() {
    if (!gameCode.trim() || !playerName.trim()) {
      alert('Please enter both game code and your name');
      return;
    }
    
    // Navigate to player view with the game code and name
    push(`/player?code=${gameCode.toUpperCase()}&name=${encodeURIComponent(playerName)}`);
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      handleJoinGame();
    }
  }
</script>

<div class="min-h-screen bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center p-4">
  <div class="max-w-4xl w-full">
    <!-- Hero Section -->
    <div class="text-center mb-12">
      <h1 class="text-6xl font-bold text-white mb-4">Quiz Platform</h1>
      <p class="text-xl text-blue-100 mb-8">
        Create engaging quizzes and play with friends in real-time
      </p>
    </div>

    <!-- Main Actions -->
    <div class="grid md:grid-cols-2 gap-8 mb-12">
      <!-- Host a Quiz -->
      <div class="bg-white rounded-xl shadow-xl p-8 text-center">
        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Host a Quiz</h2>
        <p class="text-gray-600 mb-6">
          Create and manage quizzes, then host live games for your audience
        </p>
        <button 
          class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          onclick={navigateToHost}
        >
          Start Hosting
        </button>
      </div>

      <!-- Join a Game -->
      <div class="bg-white rounded-xl shadow-xl p-8 text-center">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-4">Join a Game</h2>
        <p class="text-gray-600 mb-6">
          Enter a game code to join an active quiz session
        </p>
        
        <div class="space-y-4">
          <input
            type="text"
            placeholder="Game Code"
            bind:value={gameCode}
            onkeypress={handleKeyPress}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent uppercase text-center text-lg font-mono"
            maxlength="6"
          />
          <input
            type="text"
            placeholder="Your Name"
            bind:value={playerName}
            onkeypress={handleKeyPress}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            maxlength="20"
          />
          <button 
            class="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onclick={handleJoinGame}
            disabled={!gameCode.trim() || !playerName.trim() || isJoining}
          >
            {isJoining ? 'Joining...' : 'Join Game'}
          </button>
        </div>
      </div>
    </div>

    <!-- Additional Actions -->
    <div class="text-center">
      <button 
        class="bg-white text-blue-600 py-3 px-8 rounded-lg font-semibold hover:bg-gray-50 transition-colors shadow-lg"
        onclick={navigateToQuizzes}
      >
        Manage Quizzes
      </button>
    </div>

    <!-- Features -->
    <div class="mt-16 grid md:grid-cols-3 gap-8 text-center">
      <div class="text-white">
        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold mb-2">Real-time</h3>
        <p class="text-blue-100">Live updates and instant feedback for all participants</p>
      </div>
      
      <div class="text-white">
        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold mb-2">Easy to Use</h3>
        <p class="text-blue-100">Simple interface for both hosts and players</p>
      </div>
      
      <div class="text-white">
        <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold mb-2">Analytics</h3>
        <p class="text-blue-100">Track performance and engagement metrics</p>
      </div>
    </div>
  </div>
</div>
