<script lang="ts">
  import { onMount } from 'svelte';
  import { push } from 'svelte-spa-router';
  import { apiService } from '../services/api';
  import type { Quiz } from '../types/quiz';
  import QuizCard from '../components/QuizCard.svelte';
  import LoadingSpinner from '../components/LoadingSpinner.svelte';

  // Svelte 5 runes for state management
  let quizzes = $state<Quiz[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let showCreateModal = $state(false);
  let newQuizName = $state('');

  onMount(async () => {
    await loadQuizzes();
  });

  async function loadQuizzes() {
    isLoading = true;
    error = null;
    
    try {
      quizzes = await apiService.getQuizzes();
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load quizzes';
    } finally {
      isLoading = false;
    }
  }

  async function createNewQuiz() {
    if (!newQuizName.trim()) {
      alert('Please enter a quiz name');
      return;
    }

    try {
      const newQuiz = await apiService.createQuiz({
        name: newQuizName.trim(),
        questions: []
      });

      if (newQuiz) {
        quizzes = [newQuiz, ...quizzes];
        showCreateModal = false;
        newQuizName = '';
        // Navigate to edit the new quiz
        push(`/edit/${newQuiz.id}`);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to create quiz';
    }
  }

  async function deleteQuiz(quizId: string) {
    if (!confirm('Are you sure you want to delete this quiz?')) {
      return;
    }

    try {
      const success = await apiService.deleteQuiz(quizId);
      if (success) {
        quizzes = quizzes.filter(q => q.id !== quizId);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to delete quiz';
    }
  }

  function editQuiz(quizId: string) {
    push(`/edit/${quizId}`);
  }

  async function hostQuiz(quiz: Quiz) {
    try {
      const game = await apiService.createGame(quiz.id);
      if (game) {
        push(`/host/${game.id}`);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to create game';
    }
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      createNewQuiz();
    }
  }
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-8">
    <h1 class="text-3xl font-bold text-gray-900">My Quizzes</h1>
    <button 
      class="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
      onclick={() => showCreateModal = true}
    >
      Create New Quiz
    </button>
  </div>

  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
      <strong class="font-bold">Error:</strong>
      <span class="block sm:inline">{error}</span>
      <button 
        class="float-right text-red-700 hover:text-red-900"
        onclick={() => error = null}
      >
        ×
      </button>
    </div>
  {/if}

  {#if isLoading}
    <LoadingSpinner />
  {:else if quizzes.length === 0}
    <div class="text-center py-12">
      <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <h3 class="text-xl font-semibold text-gray-900 mb-2">No quizzes yet</h3>
      <p class="text-gray-600 mb-6">Create your first quiz to get started</p>
      <button 
        class="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        onclick={() => showCreateModal = true}
      >
        Create Your First Quiz
      </button>
    </div>
  {:else}
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {#each quizzes as quiz (quiz.id)}
        <QuizCard 
          {quiz} 
          onEdit={() => editQuiz(quiz.id)}
          onDelete={() => deleteQuiz(quiz.id)}
          onHost={() => hostQuiz(quiz)}
        />
      {/each}
    </div>
  {/if}
</div>

<!-- Create Quiz Modal -->
{#if showCreateModal}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md">
      <h2 class="text-xl font-bold text-gray-900 mb-4">Create New Quiz</h2>
      
      <div class="mb-4">
        <label for="quiz-name" class="block text-sm font-medium text-gray-700 mb-2">
          Quiz Name
        </label>
        <input
          id="quiz-name"
          type="text"
          bind:value={newQuizName}
          onkeypress={handleKeyPress}
          placeholder="Enter quiz name..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          autofocus
        />
      </div>
      
      <div class="flex justify-end space-x-3">
        <button 
          class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
          onclick={() => {
            showCreateModal = false;
            newQuizName = '';
          }}
        >
          Cancel
        </button>
        <button 
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          onclick={createNewQuiz}
          disabled={!newQuizName.trim()}
        >
          Create Quiz
        </button>
      </div>
    </div>
  </div>
{/if}
