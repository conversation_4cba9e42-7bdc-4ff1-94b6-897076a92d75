<!--TEMPLATE/COMPONENTS/TOP-NAVBAR.HTML-->
{%load static %}
<div class="navbar bg-gradient-to-br from-primary/20 to-secondary/20 text-primary shadow-md px-4 md:px-16">
  <div class="navbar-start space-x-2 items-center">
{% if user.is_authenticated %}
<div class="dropdown">
  <div tabindex="0" role="button" class="lg:hidden rounded-full cursor-pointer">
    <i class="fa-regular fa-bars text-xl mt-1"></i>
  </div>
  <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
    <li><a href="{% url 'home' %}">Home</a></li>
    <li><a href="{% url 'dashboard:dashboard' %}">Dashboard</a></li>
    <li><a href="">My Content</a></li>

  </ul>
</div>
{% endif %}
     <a href="{% url 'home' %}">  <div class="flex items-center">
            <img src="{% static 'img/logo.svg' %}" alt="oneclevermonkey Logo" class="w-8 h-8">
            <div class="font-poppins font-medium text-primary text-lg ml-2 hidden sm:inline-block">Purple Folder</div>
        </div></a>
  </div>
{% if user.is_authenticated %}
  <div class="navbar-center hidden lg:flex">
    <ul class="menu menu-horizontal px-1 font-medium text-primary">
      <li><a href="{% url 'home' %}">Home</a></li>

      <li><a href="{% url 'dashboard:dashboard' %}">Dashboard</a></li>

      <li><a href="">My Content</a></li>


    </ul>
  </div>
    {% else %}
      <div class="navbar-center hidden lg:flex">
    <ul class="menu menu-horizontal px-1">
      <li><a href="{% url 'home' %}">Home</a></li>

    </ul>
  </div>
  {% endif %}
  <div class="navbar-end">
    {% if user.is_authenticated %}
      <!-- Premium Upgrade Button - Updated to link to pricing page -->
      <a href="" class="btn btn-sm btn-primary mr-2">
        <i class="fa-regular fa-arrow-up-right-from-square mr-1"></i> Upgrade Plan
      </a>

      <div class="dropdown dropdown-end">
        <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
          <div class="w-10 rounded-full">
            <img alt="Avatar" src="https://ui-avatars.com/api/?name={{ user.first_name }}+{{ user.last_name }}&background=random" />
          </div>
        </div>
        <ul tabindex="0" class="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52">
          <li><a href="">Profile</a></li>
          <li><a href="">Logout</a></li>
            <li><a href="">Contact</a></li>
        </ul>
      </div>
    {% else %}
      <a href="" class="btn btn-ghost ml-2">Login</a>
      <a href="" class="btn btn-primary ml-2">Sign Up</a>
    {% endif %}
  </div>
</div>
{% if user.is_authenticated %}


{% endif %}
